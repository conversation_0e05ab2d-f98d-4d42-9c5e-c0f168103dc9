[project]
name = "eyecue-things-api"
version = "0.0.1"
readme = "README.md"
description = "A package for interacting with the eyecue-things-shadow table"
classifiers = [
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dynamic = ["dependencies", "optional-dependencies"]
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
requires-python = ">=3.12"

[project.urls]
Homepage = "https://bitbucket.org/fingermarkltd/eyecue-things-api"
Repository = "https://bitbucket.org/fingermarkltd/eyecue-things-api"

[tool.setuptools.packages.find]
include = ["eyecue_things_api*"]

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}
optional-dependencies = {dev = { file = ["requirements-dev.txt"] }}

[build-system]
requires = ["setuptools", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[tool.commitizen]
name = "cz_conventional_commits"
tag_format = "$version"
version_scheme = "semver"
version_provider = "pep621"
major_version_zero = false

[tool.ruff]
line-length = 88
lint.extend-select = ["ALL"]
lint.ignore = [
    "A005",
    "ANN002",
    "ANN003",
    "ANN401",
    "COM812",
    "D1",
    "D203",
    "D211",
    "D213",
    "D407",
    "EM",
    "ERA",
    "ISC001",
    "S",
    "TRY",
    "TD003",
    "FIX002",
    "UP047",
]

[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = [
    "INP001",
    "ANN201",
]

"functions/*.py" = [
    "INP001"
]

[tool.ruff.lint.flake8-tidy-imports]
ban-relative-imports = "all"

[tool.mypy]
strict = true
files = [
    "eyecue_things_api",
    "functions",
    "tests",
]
ignore_missing_imports = true
explicit_package_bases = true
check_untyped_defs = true

[[tool.mypy.overrides]]
module = 'tests.*'
disable_error_code = "var-annotated"

[[tool.mypy.overrides]]
# mypy_boto3_dynamodb does not explicitly export the types we need
module = 'eyecue_things_api.boto3_type_defs'
disable_error_code = "attr-defined"

[tool.pytest.ini_options]
addopts = "--cov=eyecue_things_api --cov=functions --cov=tests --cov-report term --cov-config=pyproject.toml"
required_plugins = ["pytest-cov"]
testpaths = ["tests"]
filterwarnings = [
    "ignore::DeprecationWarning:botocore"  # Removes datetime.datetime.utcnow() deprecated warning
]

[tool.coverage.run]
branch = true

[tool.coverage.report]
skip_empty = true
fail_under = 80
sort = "cover"
show_missing = true
exclude_also = [
    "if __name__ == .__main__.:"
]
