#!/usr/bin/env python
"""Validate the existing configuration in the things shadow table.

If you are using the fm CLI, you can run the following command to assume the role:
```bash
source <(fm aws assume-role cfa-usa)
```

Then you can run it with the following command:
```bash
./scripts/validate_existing_config > validate_existing_config.log
```

"""
from boto3 import Session
from pydantic import TypeAdapter, ValidationError

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import scan_table
from eyecue_things_api.models import ThingShadowDiscriminator

adapter = TypeAdapter(ThingShadowDiscriminator)


def validate_item(item: dict) -> None:
    try:
        adapter.validate_python(item)
    except ValidationError as e:
        site_id = item["site_id"]
        camera_id = item["camera_id"]

        # Ignore these things, should probably delete
        if any(invalid in camera_id for invalid in ["store-state-builder", "ausing"]):
            return

        print(f"Failed to parse item {site_id} ({camera_id}): \n{e}")  # noqa: T201
        print("\n")  # noqa: T201


if __name__ == "__main__":
    sess = Session()
    execution_context = Context(boto3_session=sess)

    for item in scan_table(
        table=execution_context.things_shadow_table,
    ):
        validate_item(item)
