import json
import sys
from pathlib import Path

import yaml
from aws_lambda_powertools.event_handler.openapi.models import Server

# Workaround to get commitizen to work
sys.path.insert(0, Path(__file__).parents[1].as_posix())
from eyecue_things_api.app import app


def add_api_key_requirement(schema: dict) -> dict:
    schema.setdefault("components", {}).setdefault("securitySchemes", {})[
        "x-api-key"
    ] = {
        "name": "x-api-key",
        "type": "apiKey",
        "in": "header",
    }
    schema["security"] = [{"x-api-key": []}]
    return schema


if __name__ == "__main__":
    servers = [
        Server(
            url="https://ipqtw26rgg.execute-api.ap-southeast-2.amazonaws.com/prod/api/v1/",
            description="Development",
        ),
    ]

    schema_str = app.get_openapi_json_schema(
        openapi_version="3.1.0",
        title="Eyecue Things API",
        servers=servers,
    )

    schema = json.loads(schema_str)

    schema = add_api_key_requirement(schema)

    output_path = Path(__file__).parents[1] / "openapi.yaml"

    with output_path.open("w") as file:
        yaml.dump(schema, file, default_flow_style=False)
