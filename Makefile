install:
	pip install .

install-dev:
	pip install -e .[dev]

all: lint type-check test

lint:
	python3 -m ruff check . --exclude bin,lib,scripts
	python3 -m ruff format --check . --exclude bin,lib,scripts

format:
	python3 -m ruff check --fix . --exclude bin,lib,scripts
	python3 -m ruff format . --exclude bin,lib,scripts

type-check:
	python3 -m mypy

test:
	python3 -m pytest

test-htmlcov:
	python3 -m pytest --cov-report html

deploy-dev:
	npm run deploy
