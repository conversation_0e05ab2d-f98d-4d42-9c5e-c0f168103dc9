from typing import Any

from pydantic import Field, PrivateAttr

from eyecue_things_api.models.tracker.indoor_roi import IndoorRoi
from eyecue_things_api.models.types import IndoorRoiType, UseCaseNames


class CustomerInLineStats(IndoorRoi):
    id: str = Field(
        description="The id of the ROI. Must be unique.",
        examples=["customer_waiting_area_1"],
        json_schema_extra={"placeholder": "customer_waiting_area_<id>"},
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.",
        default="floor|customer_in_line_stats",
        json_schema_extra={"immutable": True},
    )


class CustomerStats(IndoorRoi):
    id: str = Field(
        description="The id of the ROI. Must be unique.",
        examples=["customer_front_counter_1"],
        json_schema_extra={"placeholder": "customer_frount_counter_<id>"},
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.",
        default="customer|staff_customer_stats",
        json_schema_extra={"immutable": True},
    )
    group_name: str | None = Field(
        description="Name of the ROI group.",
        default="staff_customer_1",
    )


class StaffStats(IndoorRoi):
    id: str = Field(
        description="The id of the ROI. Must be unique.",
        examples=["staff_front_counter"],
        json_schema_extra={"placeholder": "staff_front_counter_<id>"},
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.",
        default="staff|staff_customer_stats",
        json_schema_extra={"immutable": True},
    )
    group_name: str | None = Field(
        description="Name of the ROI group.",
        default="staff_customer_1",
    )


class StaffCustomerStats(IndoorRoi):
    _customer: type[CustomerStats] = PrivateAttr(CustomerStats)
    _staff: type[StaffStats] = PrivateAttr(StaffStats)

    @classmethod
    def _get_model_json_schema(cls) -> list[dict[str, Any]]:
        return [
            cls._customer.model_json_schema(),
            cls._staff.model_json_schema(),
        ]


class ChipBayState(IndoorRoi):
    id: str = Field(
        description="The id of the ROI. Must be unique.",
        examples=["chip_bay_1"],
        json_schema_extra={"placeholder": "chip_bay_<id>"},
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.",
        default="chip_bay|chip_bay_state",
        json_schema_extra={"immutable": True},
    )
    extra: dict[str, Any] | None = Field(
        description="Extra parameters that can be used by the ROI.",
        default={"hsv_range": {"start": [5, 120, 80], "end": [33, 250, 220]}},
        examples=[{"hsv_range": {"start": [5, 120, 80], "end": [33, 250, 220]}}],
    )


class TableStatus(IndoorRoi):
    id: str = Field(
        description="The id of the ROI. Must be unique.",
        examples=["table_1"],
        json_schema_extra={"placeholder": "table_<id>"},
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.",
        default="table|table_status",
        json_schema_extra={"immutable": True},
    )


class WorkstationStats(IndoorRoi):
    id: str = Field(
        description="The id of the ROI. Must be unique.",
        examples=["broiler_station_1"],
        json_schema_extra={"placeholder": "<prep>_station_<id>"},
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.",
        default="workstation|workstation_stats",
        json_schema_extra={"immutable": True},
    )


class UseCasesRois:
    def __init__(self) -> None:
        self._use_cases: dict[UseCaseNames, type[IndoorRoi]] = {
            "chip_bay_state": ChipBayState,
            "customer_in_line_stats": CustomerInLineStats,
            "workstation_stats": WorkstationStats,
            "staff_customer_stats": StaffCustomerStats,
            "table_status": TableStatus,
        }

    def get_names(self) -> list[str]:
        return list(self._use_cases.keys())

    def get_schema(self, use_case: str) -> list[dict[str, Any]] | None:
        if use_case == "staff_customer_stats":
            return StaffCustomerStats._get_model_json_schema()  # type: ignore  # noqa: PGH003

        if use_case in self._use_cases:
            return [self._use_cases[use_case].model_json_schema()]
        return None
