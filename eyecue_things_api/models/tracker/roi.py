import uuid as uuid_lib
from typing import Literal

from pydantic import BaseModel, Field, model_validator

from eyecue_things_api.models.config import model_config
from eyecue_things_api.models.types import (
    Coordinates,
    Functionalities,
    ReAcquirementType,
    RoiType,
    VehicleFlow,
)

DEFAULT_VALUE_UPPER = 2
DEFAULT_VALUE_LOWER = -2


class RoiConfigFilter(BaseModel):
    max_area: int | None = Field(
        description="The maximum area of the car normalized in pixels.",
        default=DEFAULT_VALUE_UPPER,
    )
    max_height: int | None = Field(
        description="The maximum height of the car normalized in pixels.",
        default=DEFAULT_VALUE_UPPER,
    )
    max_width: int | None = Field(
        description="The maximum width of the car normalized in pixels.",
        default=DEFAULT_VALUE_UPPER,
    )
    min_age: int | None = Field(
        description="The minimum age of the car in seconds.", default=0
    )
    min_area: int | None = Field(
        description="The minimum area of the car normalized in pixels.",
        default=DEFAULT_VALUE_LOWER,
    )
    min_height: int | None = Field(
        description="The minimum height of the car normalized in pixels.",
        default=DEFAULT_VALUE_LOWER,
    )
    min_width: int | None = Field(
        description="The minimum width of the car normalized in pixels.",
        default=DEFAULT_VALUE_LOWER,
    )
    type: Literal["ROIAgeFilter", "ROIANDFilter", "ROIORFilter"] | None = Field(
        description=(
            "The type of the filter. Value that register what the type of the filter."
        ),
    )


class InteractionPoint(BaseModel):
    x: float = Field(
        description="The x coordinate of the interaction point.",
        default=0.5,
        ge=0,
        le=1,
    )
    y: float = Field(
        description="The y coordinate of the interaction point.",
        default=0.5,
        ge=0,
        le=1,
    )


class Roi(BaseModel):
    model_config = model_config

    # General ROI parameters
    allow_reenter: bool | None = Field(
        description="Whether to allow a car to reenter the ROI (Deprecated).",
        default=False,
        deprecated=True,
    )
    coordinates: list[Coordinates] = Field(  # type: ignore[valid-type]
        description="The coordinates of the ROI.",
        examples=[[[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]]],
    )
    distance_threshold: float | None = Field(
        description=(
            "Percentage used to calculate whether a car is following a trajectory or "
            "not. The final distance will be based on the size of the car, and "
            "the specified percentage, if functionality `trajectory` present. "
        ),
        ge=0,
        le=1,
        default=0,
    )
    event_threshold: int | None = Field(
        description=(
            "Specify the number of frames that the car must not have been following a"
            "trajectory to be considered and event of interest (baulk or early-leave), "
            "if functionality `trajectory` present."
        ),
        default=5,
    )
    filters: list[RoiConfigFilter] | None = Field(
        description=(
            "The filters that will be applied to the ROI. "
            "When an asset is filtered it won't generate metrics."
        ),
        default=None,
    )
    functional_zone: str | None = Field(
        description="The name of the functional zone.",
        examples=["order-canopy"],
        default=None,
    )
    functionalities: list[Functionalities] = Field(
        description="The functionalities that the ROI will have.",
        examples=["best_shot", "edge_roi", "embedding"],
        default_factory=list,
    )
    id: str = Field(
        description="The id of the ROI. Must be unique.", examples=["deliver"]
    )
    interaction_point: InteractionPoint | None = Field(
        description=(
            "The interaction point of the centre of the asset that will be "
            "used to check if an asset is inside this ROI."
        ),
        default_factory=InteractionPoint,
    )
    lane_number: int | None = Field(
        description=(
            "The lane number of this ROI. Used to report with the events generated."
        ),
        default=1,
    )
    uuid: uuid_lib.UUID = Field(
        description="UUID for the ROI", default_factory=uuid_lib.uuid4
    )
    rtype: ReAcquirementType | None = Field(
        description="The re-acquirement algorithm to be used.",
        examples=["fifo", "visual"],
        default=None,
    )
    time_between_events_threshold: float | None = Field(
        description=(
            "The time in seconds that must pass between events for the ROI to "
            "create a new event (Deprecated)."
        ),
        default=0,
        deprecated=True,
    )
    type: RoiType = Field(
        description="The type of the ROI.", examples=["DWELL", "DELIVER"]
    )
    # HVI Parameters
    device_interaction: bool | None = Field(
        description=(
            "Enable this feature if the Device-Driver Interaction "
            "occurs within the ROI instead of Human-Vehicle "
            "Interaction."
        ),
        default=False,
    )
    enable_hvi_dfa: bool | None = Field(
        description=(
            "By default, HVI with DFA is only enabled for cfa-usa clients. "
            "Use this variable to enable for other clients."
        ),
        default=False,
    )
    hvi_angle_threshold: float | None = Field(
        description=(
            "The threshold angle between the orthogonal vector of "
            "the vehicle flow and a vector composed of the center "
            "of the vehicle's bounding box and the center of the "
            "person's bounding box."
        ),
        default=30.0,
        ge=0,
        le=180,
    )
    hvi_dfa_keep_alive: int | None = Field(
        description=(
            "The amount of (consecutive) frames that the DFA should keep "
            "tracking an event if no interaction (between the person and "
            "vehicle) occurs."
        ),
        default=6,
        ge=1,
    )
    hvi_dfa_min_distance: float | None = Field(
        description=(
            "The minimum distance between a person and a vehicle to be "
            "considered an interaction."
        ),
        default=1.5,
        ge=0,
    )
    hvi_dfa_min_time: int | None = Field(
        description=(
            "The minimum amount of frames that an interaction (between a "
            "vehicle and a person) should happen to start an event."
        ),
        default=9,
        ge=0,
    )
    hvi_dist_velocity_penalty: bool | None = Field(
        description=(
            "Whether to penalize the distance between a person and a vehicle "
            "by the velocity of the vehicle."
        ),
        default=False,
    )
    hvi_filter_by_angle: bool | None = Field(
        description=(
            "Should filter HVI by checking angle of the vehicle-person vector "
            "and the vehicle flow."
        ),
        default=True,
    )
    hvi_filter_person_roi: bool | None = Field(
        description=(
            "Should only consider HVIs where the person is in the same ROI of "
            "the vehicle."
        ),
        default=False,
    )
    hvi_select_stopped_car: bool | None = Field(
        description=("Should only consider HVIs where the vehicle is stopped."),
        default=False,
    )
    hvi_select_stopped_person: bool | None = Field(
        description=("Should only consider HVIs where the person is stopped."),
        default=False,
    )
    hvi_threshold_distance: float | None = Field(
        description=(
            "Threshold distance between the centroids of a cars and a persons "
            "bounding boxes is used to determine whether an interaction between "
            "a human and a vehicle is taking place. This is only considered when "
            "the overhead option is enabled. (This will be deprecated)."
        ),
        default=1.5,
        deprecated=True,
    )
    hvi_use_flow_points: bool | None = Field(
        description=(
            "Should use better points (instead of centroids) to measure "
            "distance between vehicle and person (this feature require "
            "configuration of hvi_filter_by_angle)."
        ),
        default=True,
    )
    hvi_vehicle_flow: VehicleFlow | None = Field(
        description=(
            "The direction of the flow in the camera using clock pointer "
            "direction notation."
        ),
        default=6,
    )
    hvi_width_penalty: bool | None = Field(
        description=(
            "For hvi distance estimation, if a car is too close to the camera "
            "then the width of the car is penalized."
        ),
        default=True,
    )
    interaction_roi: bool | None = Field(
        description=(
            "Whether the ROI is an interaction ROI. I.e If the ROI is an interaction "
            "ROI it will check if there's human-vehicle interaction inside this ROI."
        ),
        default=False,
    )
    overhead: bool | None = Field(
        description=(
            "Whether this camera is an overhead camera. This changes how the "
            "human-vehicle detection is calculated. (This will be deprecated)."
        ),
        default=False,
        deprecated=True,
    )
    # Queue Parameters
    change_no_queue_n_frames: float | None = Field(
        description=(
            "Number of frames in a row with no cars in queue to change the final "
            "status of the ROI to False (no queue)."
        ),
        alias="change_NO_queue_n_frames",
        default=20,
    )
    change_to_queue_n_frames: float | None = Field(
        description=(
            "Number of frames in a row with cars in queue to change the final "
            "status of the ROI to True (queue present)."
        ),
        default=5,
    )
    queue_level: int | None = Field(
        description="The level of the queue, positive number.",
        examples=[1, 2, 3, 4, 5],
        gt=0,
        default=None,
    )
    rel_speed_threshold: float | None = Field(
        description=(
            "Maximum relative speed above which the cars are not considered to be in "
            "queue (there is no queue if the cars are moving too fast). Relative speed "
            "is calculated as the ratio of the car's movement in pixels in the "
            "last frame to the car's size in pixels."
        ),
        default=0.02,
    )
    # Re-ID Parameters
    linear: bool | None = Field(
        description=(
            "Whether this is a linear drive thru. Eyecue don't "
            "allow assets to just appear in the middle of the "
            "reid node, meaning they must have come from a "
            "previous node. When this values is set to False "
            "Eyecue allow an asset to start from this node."
        ),
        default=True,
    )
    lookup_depth: int | None = Field(
        description=(
            "Depth to look for a candidate in the pool of "
            "candidates. When set to -1 every candidate "
            "will be selected."
        ),
        default=2,
        ge=-1,
    )
    order: str | None = Field(
        description=(
            "The order of the ROI. Used in best shot to represent the order of "
            "the REID Graph."
        ),
        examples=["A.B.C", "A.B"],
        default=None,
        validate_default=True,
    )
    visual_threshold: float | None = Field(
        description=(
            "Threshold to consider a candidate a match. "
            "The lower it gets more confident it is."
        ),
        default=1,
        ge=0,
        le=1,
    )

    @model_validator(mode="after")
    def validate_order(self) -> "Roi":
        if "best_shot" in self.functionalities and self.order is None:
            raise ValueError(
                "order cannot be None when functionality includes best shot"
            )

        return self
