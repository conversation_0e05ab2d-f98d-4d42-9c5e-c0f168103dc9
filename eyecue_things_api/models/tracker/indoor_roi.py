import uuid as uuid_lib
from typing import Any

from pydantic import BaseModel, Field

from eyecue_things_api.models.config import model_config
from eyecue_things_api.models.types import (
    Coordinates,
    IndoorRoiType,
)


class IndoorRoi(BaseModel):
    model_config = model_config

    coordinates: list[Coordinates] = Field(  # type: ignore[valid-type]
        description="The coordinates of the ROI.",
        examples=[[[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]]],
        json_schema_extra={"visible": True},
    )
    extra: dict[str, Any] | None = Field(
        description="Extra parameters that can be used by the ROI.",
        default=None,
        json_schema_extra={"advanced": True},
    )
    group_id: uuid_lib.UUID | None = Field(
        description="UUID of the ROI group.",
        default=None,
        json_schema_extra={"immutable": True},
    )
    group_name: str | None = Field(
        description="Name of the ROI group.",
        default=None,
    )
    id: str = Field(
        description="The id of the ROI. Must be unique.", examples=["deliver"]
    )
    type: IndoorRoiType = Field(
        description="The type of the ROI.",
        examples=["workstation|workstation_stats"],
        json_schema_extra={"immutable": True},
    )
    uuid: uuid_lib.UUID = Field(
        description="UUID for the ROI",
        json_schema_extra={"immutable": True},
    )
    location: str = Field(
        description="Location of the ROI.",
        examples=["kitchen", "dining_room"],
        json_schema_extra={"placeholder": "kitchen"},
    )
    minimum_mod_iou: float | None = Field(
        description="Minimum intersection between the person and the RoI.",
        examples=["0.3", "0.5"],
        ge=0,
        le=1,
        default=None,
        json_schema_extra={"placeholder": 0.3, "advanced": True},
    )

    @classmethod
    def get_model_json_schema(
        cls,
        uuid: uuid_lib.UUID | None = None,
        group_id: uuid_lib.UUID | None = None,
    ) -> dict[str, Any]:
        schema = super().model_json_schema()
        if uuid is not None:
            schema["properties"]["uuid"]["default"] = str(uuid)
        else:
            schema["properties"]["uuid"]["default"] = str(uuid_lib.uuid4())
        if group_id is not None:
            schema["properties"]["groupId"]["default"] = str(group_id)
        return schema
