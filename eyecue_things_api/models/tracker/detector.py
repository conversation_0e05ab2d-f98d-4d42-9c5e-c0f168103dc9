from enum import StrEnum
from typing import Literal

from pydantic import BaseModel, Field


class ClassName(StrEnum):
    car = "car"
    motorcycle = "motorcycle"
    person = "person"
    trailer = "trailer"
    truck = "truck"


class DetectorName(StrEnum):
    indoor_yolov4 = "indoor-yolov4"
    rtdetr = "rtdetr"
    yolov4 = "yolov4"


class Classes(BaseModel):
    name: ClassName | None = Field(
        description="Name of the class.", examples=["car", "person"]
    )
    max_age: int | None = Field(
        description=(
            "Maximum age of the asset (in seconds) before it "
            "gets deleted if it is not updated."
        ),
        ge=1,
        default=6,
    )
    min_hits: int | None = Field(
        description="Minimum number of hits before the asset is considered valid.",
        ge=1,
        default=3,
    )
    threshold: float | None = Field(
        description="Threshold for the class.", default=0.5, le=1.0, ge=0.0
    )


class Detector(BaseModel):
    classes: list[Classes] = Field(
        description="The classes to detect.",
        default=[
            Classes(
                name=ClassName.car,
                threshold=0.5,
            ),
            Classes(
                name=ClassName.person,
                threshold=0.5,
            ),
        ],
    )
    detector_name: DetectorName | None = Field(
        description="The name of the detector.",
        examples=["rtdetr", "yolov4"],
        default=DetectorName.yolov4,
    )
    routing_key: Literal["eyeq-detector"] | None = Field(
        description="The routing key of the detector.",
        default="eyeq-detector",
        deprecated=True,
    )


class IndoorDetector(BaseModel):
    classes: list[Classes] = Field(
        description="The classes to detect.",
        default=[
            Classes(
                name=ClassName.person,
                threshold=0.3,
            ),
        ],
    )
    conf_threshold: float | None = Field(
        description="Confidence threshold (specific to Tracker Features)",
        default=0.5,
        le=1.0,
        ge=0.0,
    )
    detector_name: DetectorName | None = Field(
        description="The name of the detector.",
        examples=["rtdetr", "yolov4"],
        default=DetectorName.indoor_yolov4,
    )
    iou_threshold: float | None = Field(
        description="IoU threshold (specific to Tracker Features)",
        default=0.5,
        le=1.0,
        ge=0.0,
    )
    routing_key: Literal["eyeq-detector"] | None = Field(
        description="The routing key of the detector.",
        default="eyeq-detector",
        deprecated=True,
    )
