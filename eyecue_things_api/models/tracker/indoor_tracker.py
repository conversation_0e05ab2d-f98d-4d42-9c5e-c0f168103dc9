import uuid as uuid_lib
from typing import Any, Literal

from pydantic import BaseModel, Field, field_validator, model_validator

from eyecue_things_api.models.base import ThingEntityModel
from eyecue_things_api.models.tracker.camera import Camera
from eyecue_things_api.models.tracker.detector import IndoorDetector
from eyecue_things_api.models.tracker.indoor_roi import IndoorRoi
from eyecue_things_api.models.types import TrackerId


class Config(BaseModel):
    log_level_debug: bool | None = Field(
        description="Whether to log debug messages or not.",
        default=False,
    )
    log_output_path: str | None = Field(
        description="The path where the logs will be saved.",
        examples=["/tmp/eyeq-tracker-logs"],
        default="logs.log",
    )


class Tracking(BaseModel):
    budget: int | None = Field(
        default=100, description="Budget for the nearest neighbors.", ge=0
    )
    conf_threshold: float | None = Field(
        default=0.3, description="Threshold for the confidence.", le=1.0, ge=0.0
    )
    enabled: bool | None = Field(default=True, description="Should enable tracking.")
    iou_threshold: float | None = Field(
        default=0.1, description="Threshold for the IOU.", le=1.0, ge=0.0
    )
    iou_threshold_for_unmatched_detections: float | None = Field(
        default=0.01,
        description="Threshold for the IOU for unmatched detections.",
        le=1.0,
        ge=0.0,
    )
    max_age: int | None = Field(
        default=70,
        description=(
            "Maximum age of the tracker before it gets deleted if it is not updated."
        ),
        ge=0,
    )
    max_distance: float | None = Field(
        default=0.2, description="Maximum distance for the tracker.", le=1.0, ge=0.0
    )
    max_iou_distance: float | None = Field(
        default=0.7, description="Maximum IOU distance.", le=1.0, ge=0.0
    )
    model: str = Field(
        default="deep_sort", description="The model to use for the tracker."
    )
    n_init: int | None = Field(
        default=3,
        description=("Minimum number of hits before the tracker is considered valid."),
    )
    reid_enabled: bool | None = Field(default=True, description="Should perform reid.")
    version: str | None = Field(
        default="qsr-1.1",
        description=("The version of the model to use for the tracker."),
    )


class IndoorTracker(ThingEntityModel):
    camera: Camera = Field(description="The configuration options for the camera.")
    camera_id: TrackerId = Field(
        description="The id of the tracker.",
        examples=[
            "eyeq-tracker-fm-mcd-aus-1305-camera15",
            "eyeq-tracker-fm-cfa-usa-01490-camera148",
        ],
    )
    config: Config = Field(
        description="The configuration options for the application.",
        default_factory=Config,
    )
    detector: IndoorDetector = Field(
        description="The configuration options for the detector.",
        default_factory=IndoorDetector,
    )
    rois: list[IndoorRoi] = Field(
        description="The configuration options for the ROIs.", default_factory=list
    )
    service: Literal["Indoor"] = Field(
        description="The service that is running on camera (eyecue or indoor).",
        default="Indoor",
    )
    tracking: Tracking = Field(
        description="The configuration options for the tracking.",
        default_factory=Tracking,
    )
    uuid: uuid_lib.UUID = Field(
        description="UUID for the tracker object", default_factory=uuid_lib.uuid4
    )

    # mypy: ignore-errors
    @model_validator(mode="before")
    @classmethod
    def set_camera_name(cls, values: dict[str, Any]) -> dict[str, Any]:
        """Set camera.name using site_id and camera_id."""
        camera_data = values.get("camera")
        camera_id = values.get("camera_id")
        site_id = values.get("site_id")

        assert camera_data is not None
        assert camera_id is not None
        assert site_id is not None
        # Convert to Camera instance (it is a dict when root_validator is running)
        if not isinstance(camera_data, Camera):
            camera_data = Camera(**camera_data)

        camera_number = camera_id.split("-camera")[1]
        camera_name = f"{site_id}-{camera_number}"
        values["camera"] = camera_data.model_copy(update={"name": camera_name})
        return values

    @field_validator("rois", mode="after")
    @classmethod
    def validate_roi(cls, rois: list[IndoorRoi]) -> list[IndoorRoi]:
        """Validate that the ROI IDs are unique."""
        roi_ids = [roi.id for roi in rois]
        # Find duplicate ROI IDs. Sort it so that the error message is deterministic.
        duplicates = sorted(
            roi_id for roi_id in set(roi_ids) if roi_ids.count(roi_id) > 1
        )
        if duplicates:
            raise ValueError(f"Duplicate ROI IDs found: {', '.join(duplicates)}")

        roi_ids = [roi.uuid for roi in rois]
        # Find duplicate ROI IDs. Sort it so that the error message is deterministic.
        duplicates = sorted(
            roi_id for roi_id in set(roi_ids) if roi_ids.count(roi_id) > 1
        )
        if duplicates:
            raise ValueError(f"Duplicate ROI UUIDs found: {', '.join(duplicates)}")
        return rois
