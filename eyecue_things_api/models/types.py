from typing import Annotated, Any, Literal

from pydantic import (
    AfterValidator,
    AnyUrl,
    BeforeValidator,
    Field,
    StringConstraints,
    UrlConstraints,
    confloat,
)

SITE_ID_REGEX = r"fm-[a-z]{3}(-[a-z]{2,3})?-\d{3,5}"

SITE_ID_PATTERN = rf"^{SITE_ID_REGEX}$"
SERVER_ID_PATTERN = rf"^eyeq-server-{SITE_ID_REGEX}$"
TRACKER_ID_PATTERN = rf"^eyeq-tracker-{SITE_ID_REGEX}-camera\d{{1,4}}$"

AnyRtspUrl = Annotated[AnyUrl, UrlConstraints(allowed_schemes=["rtsp", "rtsps"])]

SiteId = Annotated[str, Field(pattern=SITE_ID_PATTERN)]

# The camera_id column in eyecue-things-shadow can be either a tracker or a server
ServerId = Annotated[str, Field(pattern=SERVER_ID_PATTERN)]
TrackerId = Annotated[str, Field(pattern=TRACKER_ID_PATTERN)]
ThingId = Annotated[
    ServerId | TrackerId,
    Field(
        description=(
            "The database id of the camera. Note that this this is slightly confusing "
            "but for consistency we use the term 'camera_id' for both servers and "
            "trackers as this is the column name in the eyecue-things-shadow table."
        )
    ),
]

Coordinates = Annotated[  # type: ignore[valid-type]
    list[confloat(ge=0.0, le=1.0)],  # type: ignore[misc]
    Field(min_length=2, max_length=2),
]

Functionalities = Literal[
    "best_shot",
    "edge_roi",
    "embedding",
    "entry_count",
    "queue",
    "stream_roi",
    "timer",
    "trajectory_roi",
]

_RoiTypes: set[str] = {
    "BEST_SHOT",
    "COUNTING_ROI",
    "DELIVER",
    "DELIVER_WINDOW",
    "DWELL",
    "DWELL_ALERT",
    "EDGE_ROI",
    "EGRESS",
    "EMBEDDING",
    "ENTRY",
    "EXIT",
    "FINISH",
    "INGRESS",
    "MARK",
    "NONE",
    "ORDER",
    "PAYMENT",
    "PREDELIVER",
    "PREORDER",
    "PRESENTER",
    "PULL_FORWARD",
    "QUEUE",
    "START",
    "TIMER",
    "TRAJECTORY",
    "TRAJECTORY_ROI",
    "WAITING",
}


def roi_before_validator(type_: Any) -> Any:
    if isinstance(type_, str):
        return type_.replace("-", "_")
    return type_


def roi_after_validator(type_: str) -> str:
    if type_ not in _RoiTypes:
        # May need to ignore this error as we have many outdated configs
        # that are using outdated roi types.
        raise ValueError(
            f"type must be one of [{', '.join(sorted(_RoiTypes))}], not {type_}"
        )
    return type_


RoiType = Annotated[
    str,
    StringConstraints(strip_whitespace=True, to_upper=True),
    BeforeValidator(roi_before_validator),
    AfterValidator(roi_after_validator),
]

VehicleFlow = Annotated[
    int,
    Field(
        ge=1,
        le=12,
    ),
]

ReAcquirementType = Literal[
    "fifo",
    "tbfifo",
    "lofi",
    "visual",
    "none",
]

MainFlowDirection = Annotated[
    str,
    StringConstraints(strip_whitespace=True, to_lower=True),
    Literal["x+", "x-", "y+", "y-"],
]

IndoorRoiType = Literal[
    "chip_bay|chip_bay_state",
    "customer|staff_customer_stats",
    "floor|customer_in_line_stats",
    "staff|staff_customer_stats",
    "table|table_status",
    "workstation|workstation_stats",
]

UseCaseNames = Literal[
    "chip_bay_state",
    "customer_in_line_stats",
    "workstation_stats",
    "staff_customer_stats",
    "table_status",
]

UseCaseStatus = Literal["Available", "Coming Soon", "Disabled"]
OnetoFiveDigitStr = Annotated[str, Field(pattern=r"^\d{1,5}$")]
