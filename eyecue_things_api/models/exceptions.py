import uuid as uuid_lib

from eyecue_things_api.models import ThingShadowDiscriminator
from eyecue_things_api.models.types import SiteId, ThingId


class EyecueThingsApiError(Exception):
    """Base class for all exceptions in the eyecue-things-api package."""


class ItemExistsError(EyecueThingsApiError):
    """Raised when an item already exists in the database."""

    def __init__(self, **kwargs) -> None:
        details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
        self.message = f"Item with {details} already exists"
        super().__init__(self.message)


class ItemNotFoundError(EyecueThingsApiError):
    """Raised when an item is not found in the database."""

    def __init__(self, site_id: SiteId, thing_id: ThingId) -> None:
        self.message = (
            f"Item with site_id={site_id} and camera_id={thing_id} does not exist"
        )
        super().__init__(self.message)


class ROINotFoundError(EyecueThingsApiError):
    """Raised when a ROI is not found in the database."""

    def __init__(
        self, site_id: SiteId, thing_id: ThingId, roi_uuid: uuid_lib.UUID
    ) -> None:
        self.message = (
            f"ROI with UUID {roi_uuid} does not exist in "
            f"site_id={site_id} and camera_id={thing_id}"
        )
        super().__init__(self.message)


class ROIExistsError(EyecueThingsApiError):
    """Raised when creating an RoI with the same UUID as another on the database."""

    def __init__(
        self, site_id: SiteId, thing_id: ThingId, roi_uuid: uuid_lib.UUID
    ) -> None:
        self.message = (
            f"ROI with UUID {roi_uuid} already exists in "
            f"site_id={site_id} and camera_id={thing_id}"
        )
        super().__init__(self.message)


class ItemUpdateError(EyecueThingsApiError):
    """Raised when trying to update an item."""

    def __init__(
        self,
        **kwargs,
        # item_timestamp: datetime | None,
    ) -> None:
        details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
        self.message = f"Unable to update item with {details}. \
            Either the item does not exist or it  \
            has been updated since it was last read."
        # if item_timestamp is not None:
        #    self.message += f" last_updated_timestamp={item_timestamp.isoformat()}"
        super().__init__(self.message)


class ItemDeleteError(EyecueThingsApiError):
    """Raised when trying to delete an item."""

    def __init__(self, **kwargs) -> None:
        details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
        self.message = (
            f"Unable to delete item with {details}. Has the item been deleted already?"
        )
        super().__init__(self.message)


class ModelTypeError(EyecueThingsApiError):
    """Raised when the item is not of the expected type."""

    def __init__(
        self,
        expected_type: type[ThingShadowDiscriminator]
        | list[type[ThingShadowDiscriminator]],
        actual_type: type[ThingShadowDiscriminator],
    ) -> None:
        if isinstance(expected_type, list):
            expected_types = " or ".join(t.__name__ for t in expected_type)
        else:
            expected_types = expected_type.__name__
        self.message = (
            f"Expected item of type {expected_types} or  "
            f"but got item of type {actual_type.__name__}"
        )
        super().__init__(self.message)
