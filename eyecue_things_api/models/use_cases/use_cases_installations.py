from pydantic import (
    Field,
)

from eyecue_things_api.models.types import SiteId
from eyecue_things_api.models.use_cases.base import RecordEntityBase
from eyecue_things_api.models.use_cases.types import RoiInfo, UseCaseInstallationId


class UseCaseInstallation(RecordEntityBase):
    site_id: SiteId = Field(
        description="The site_id of the installation.",
    )
    installation_id: UseCaseInstallationId = Field(
        description="The use case and installation UUIDs combined.",
        examples=["usecase#<uuid>|installation#<uuid>"],
    )
    rois: list[RoiInfo] = Field(
        description="The rois that are linked to the installation.",
        examples=[
            "[{'camera_id': '015', 'site_id': 'fm-mcd-aus-01222',\
                'roi_id': '<uuid>'}, ...]"
        ],
    )
    author_name: str = <PERSON>(
        description="The author of the installation.",
        examples=["<PERSON>"],
    )
