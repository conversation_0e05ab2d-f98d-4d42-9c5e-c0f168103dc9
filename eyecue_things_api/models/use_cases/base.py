from datetime import UTC, datetime

from pydantic import (
    AwareDatetime,
    BaseModel,
    Field,
    SerializationInfo,
    field_serializer,
)


class RecordEntityBase(BaseModel):
    last_updated_timestamp: AwareDatetime = Field(
        description="The timestamp of the last update.",
        default_factory=lambda: datetime.now(UTC),
    )

    @field_serializer("last_updated_timestamp")
    def serialize_last_updated_timestamp(
        self,
        last_updated_timestamp: AwareDatetime,
        info: SerializationInfo,
    ) -> AwareDatetime | str:
        """Serialize the last_updated_timestamp to UTC timezone in ISO 8601 format."""
        if info.mode_is_json():
            return last_updated_timestamp.astimezone(UTC).isoformat()

        return last_updated_timestamp.astimezone(UTC)
