from typing import Any

from pydantic import BaseModel, Field, model_validator

from eyecue_things_api.models.base import ThingEntityModel
from eyecue_things_api.models.types import ServerId


class Configuration(BaseModel):
    aws_iot: bool | None = Field(
        description="Whether to use AWS IoT Broker.",
        default=True,
    )
    clean_up_on_scan: bool | None = Field(
        description=(
            "Whether to clean up Eyecue server on scan of the QR code on the frontend."
        ),
        default=False,
    )
    local_iot: bool | None = Field(
        description="Whether to use local IoT Broker.",
        default=True,
        deprecated=True,
    )
    output_logs: str | None = Field(
        description="Path to output logs.",
        default="/root/logs",
    )
    topic: str | None = Field(
        description="The AWS IoT topic to publish to.",
        default="",
    )


class VisualComparator(BaseModel):
    distance_metric: str | None = Field(
        description="Which distance metric should be used for re-id.",
        examples=["cosine", "euclidean"],
        default="cosine",
    )
    input_size: tuple[int, int] | None = Field(
        description="The size of the input image.",
        examples=[(256, 256)],
        default=(256, 256),
    )


class Server(ThingEntityModel):
    camera_id: ServerId = Field(
        description="The id of the server",
        examples=[
            "eyeq-server-fm-mcd-aus-1305",
            "eyeq-server-fm-cfa-usa-01490",
        ],
    )
    configuration: Configuration | None = Field(
        description="The configuration options for the server.",
        default_factory=Configuration,
    )
    debug_level: bool | None = Field(
        description="Whether to print debug logs",
        default=False,
        deprecated=True,
    )
    visual_comparator: VisualComparator | None = Field(
        description="The configuration options for the visual comparator.",
        default_factory=VisualComparator,
    )

    # mypy: ignore-errors
    @model_validator(mode="before")
    @classmethod
    def set_topic_name(cls, values: dict[str, Any]) -> dict[str, Any]:
        """Set configuration.topic using site_id."""
        site_id = values.get("site_id")
        config_data = values.get("configuration") or {}

        assert config_data is not None
        assert site_id is not None
        # Convert to Configuration instance
        # (it is a dict when root_validator is running)
        if not isinstance(config_data, Configuration):
            config_data = Configuration(**config_data)

        values["configuration"] = config_data.model_copy(update={"topic": site_id})
        return values
