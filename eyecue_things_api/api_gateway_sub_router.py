from typing import Any

from aws_lambda_powertools.event_handler.api_gateway import Router
from aws_lambda_powertools.event_handler.router import APIGatewayRouter


class APIGatewaySubRouter(APIGatewayRouter):
    def __init__(self) -> None:
        super().__init__()
        self._sub_routers: list[Router] = []

    def include_router(self, router: Router) -> None:
        """Include another router into the current router.

        Args:
            router (Self): Router to include

        """
        self._sub_routers.append(router)

        for route_key, method in router._routes.items():  # noqa: SLF001
            register_route = self.route(*route_key)
            register_route(method)

    def append_context(self, **additional_context) -> None:
        """Append key=value data as routing context."""
        super().append_context(**additional_context)

        # Propagate context to sub routers
        for sub_router in self._sub_routers:
            sub_router.append_context(**additional_context)

    def clear_context(self) -> None:
        """Reset routing context."""
        super().clear_context()

        # Clear context for sub routers
        for sub_router in self._sub_routers:
            sub_router.clear_context()

    def __setattr__(self, name: str, value: Any) -> None:
        """Set attribute value.

        Workaround to catch `router.context = self.context` and propagate to sub routers
        https://github.com/aws-powertools/powertools-lambda-python/blob/5665d4393348c1f4c9a5d71d58b4fc570f19a563/aws_lambda_powertools/event_handler/api_gateway.py#L2412

        Args:
            name (str): Attribute name
            value (Any): Attribute value

        """
        if name == "context" and hasattr(self, "_sub_routers"):
            # Check if _sub_routers is already set before accessing it
            for sub_router in self._sub_routers:
                sub_router.context = value

        super().__setattr__(name, value)
