"""Provides modified type definitions from the mypy_boto3_dynamodb package.

The key difference is that the `Item` attribute is excluded from the type definitions
which enables us to pass in the item as a separate argument to the functions in
eyecue_things_api/dynamodb.py.

So rather than:

```python
event: dict = {...}
item = update_table_item(
    table=execution_context.things_shadow_table,
    Item=event,
)
```

We can do:

```python
event: Tracker = Tracker(...)
item = update_table_item(
    table=execution_context.things_shadow_table,
    item=event,
)
```

NOTE: We cannot override the original type definitions in the mypy_boto3_dynamodb
package, as you cannot subclass TypedDicts in Python.

"""

from collections.abc import Mapping, Sequence
from typing import NotRequired, TypedDict

from mypy_boto3_dynamodb.type_defs import (
    ConditionalOperatorType,
    ConditionBaseImportTypeDef,
    ExpectedAttributeValueTableTypeDef,
    ReturnConsumedCapacityType,
    ReturnItemCollectionMetricsType,
    ReturnValuesOnConditionCheckFailureType,
    ReturnValueType,
    TableAttributeValueTypeDef,
)


class PutItemInputTablePutItemTypeDef(TypedDict):
    """A type definition for the PutItem method.

    Modified to exclude the Item attribute, which is required.
    """

    Expected: NotRequired[Mapping[str, ExpectedAttributeValueTableTypeDef]]
    ReturnValues: NotRequired[ReturnValueType]
    ReturnConsumedCapacity: NotRequired[ReturnConsumedCapacityType]
    ReturnItemCollectionMetrics: NotRequired[ReturnItemCollectionMetricsType]
    ConditionalOperator: NotRequired[ConditionalOperatorType]
    ConditionExpression: NotRequired[ConditionBaseImportTypeDef]
    ExpressionAttributeNames: NotRequired[Mapping[str, str]]
    ExpressionAttributeValues: NotRequired[Mapping[str, TableAttributeValueTypeDef]]
    ReturnValuesOnConditionCheckFailure: NotRequired[
        ReturnValuesOnConditionCheckFailureType
    ]


class DeleteItemInputTableDeleteItemTypeDef(TypedDict):
    """A type definition for the DeleteItem method.

    Modified to exclude the Key attribute, which is required.
    """

    Expected: NotRequired[Mapping[str, ExpectedAttributeValueTableTypeDef]]
    ConditionalOperator: NotRequired[ConditionalOperatorType]
    ReturnValues: NotRequired[ReturnValueType]
    ReturnConsumedCapacity: NotRequired[ReturnConsumedCapacityType]
    ReturnItemCollectionMetrics: NotRequired[ReturnItemCollectionMetricsType]
    ConditionExpression: NotRequired[ConditionBaseImportTypeDef]
    ExpressionAttributeNames: NotRequired[Mapping[str, str]]
    ExpressionAttributeValues: NotRequired[Mapping[str, TableAttributeValueTypeDef]]
    ReturnValuesOnConditionCheckFailure: NotRequired[
        ReturnValuesOnConditionCheckFailureType
    ]


class GetItemInputTableGetItemTypeDef(TypedDict):
    """A type definition for the GetItem method.

    Modified to exclude the Key attribute, which is required.
    """

    AttributesToGet: NotRequired[Sequence[str]]
    ConsistentRead: NotRequired[bool]
    ReturnConsumedCapacity: NotRequired[ReturnConsumedCapacityType]
    ProjectionExpression: NotRequired[str]
    ExpressionAttributeNames: NotRequired[Mapping[str, str]]
