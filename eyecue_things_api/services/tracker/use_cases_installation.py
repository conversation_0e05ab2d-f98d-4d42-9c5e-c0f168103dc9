from pydantic import <PERSON><PERSON><PERSON>pter

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import (
    delete_table_item,
    insert_table_item,
    query_table_parsed,
    scan_table_parsed,
    update_table_item,
)
from eyecue_things_api.exceptions.errors import ValidationError
from eyecue_things_api.models.types import SiteId
from eyecue_things_api.models.use_cases.types import UseCaseInstallationId
from eyecue_things_api.models.use_cases.use_cases_installations import (
    UseCaseInstallation,
)
from eyecue_things_api.util import parse_uuid


class UseCasesInstallationService:
    def __init__(self, context: Context) -> None:
        self._context: Context = context

    def get_all_use_cases_installations(self) -> list[UseCaseInstallation]:
        return scan_table_parsed(
            model=UseCaseInstallation,
            table=self._context.use_cases_installations_table,
        )

    def get_all_use_case_installations_filtered(
        self, site_id: SiteId | None, use_case_id: str | None
    ) -> list[UseCaseInstallation]:
        installation_id = None
        if use_case_id is not None:
            use_case_uuid = parse_uuid(use_case_id)
            installation_id = f"usecase#{use_case_uuid!s}"
        return query_table_parsed(
            model=UseCaseInstallation,
            table=self._context.use_cases_installations_table,
            site_id=site_id,
            installation_id=installation_id,
        )

    def create(self, use_case_installation: UseCaseInstallation) -> UseCaseInstallation:
        use_case_installation = UseCaseInstallation.model_validate(
            use_case_installation
        )
        return insert_table_item(
            table=self._context.use_cases_installations_table,
            item=use_case,
            site_id=use_case_installation.site_id,
            installation_id=use_case_installation.installation_id,
        )

    def update(
        self,
        installation_id: UseCaseInstallationId,
        use_case_installation: UseCaseInstallation,
    ) -> UseCaseInstallation:
        if installation_id != str(use_case_installation.installation_id):
            return ValidationError(
                "The request parameter installation UUID \
                    and the installation UUID don't match."
            )
        use_case_installation = UseCaseInstallation.model_validate(
            use_case_installation
        )
        return update_table_item(
            table=self._context.use_cases_installations_table,
            item=use_case_installation,
            installation_id=use_case_installation.installation_id,
        )

    def delete(self, installation_id: UseCaseInstallationId) -> None:
        validated_installation_id: UseCaseInstallationId = TypeAdapter(
            UseCaseInstallationId
        ).validate_python(str(installation_id))
        return delete_table_item(
            table=self._context.use_cases_installations_table,
            installation_id=str(validated_installation_id),
        )
