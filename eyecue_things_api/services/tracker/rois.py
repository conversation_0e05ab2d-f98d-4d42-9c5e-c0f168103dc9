from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import get_tracker_item, update_table_item
from eyecue_things_api.exceptions.errors import ValidationError
from eyecue_things_api.models.discriminator import (
    CameraTracker,
    CameraTrackerAdapter,
    TrackerRoi,
    TrackerRoiAdapter,
)
from eyecue_things_api.models.exceptions import ROIExistsError
from eyecue_things_api.models.tracker.indoor_roi import IndoorRoi
from eyecue_things_api.models.tracker.indoor_tracker import IndoorTracker
from eyecue_things_api.models.tracker.roi import Roi
from eyecue_things_api.models.tracker.tracker import Tracker
from eyecue_things_api.util import (
    find_roi,
    get_thing_record_name,
    recursive_merge_with_null_deletions,
)


class RoiService:
    def __init__(self, context: Context) -> None:
        self._context: Context = context

    def get(self, site_id: str, camera_id: str, roi_id: str) -> TrackerRoi:
        camera_tracker = get_tracker_item(
            table=self._context.things_shadow_table,
            site_id=site_id,
            tracker_id=get_thing_record_name(site_id, camera_id),
        )
        _, roi = find_roi(camera_tracker, roi_id, silent=True)
        return roi

    def create(self, site_id: str, camera_id: str, roi: TrackerRoi) -> CameraTracker:
        item = get_tracker_item(
            table=self._context.things_shadow_table,
            site_id=site_id,
            tracker_id=get_thing_record_name(site_id, camera_id),
        )
        if (isinstance(item, IndoorTracker) and not isinstance(roi, IndoorRoi)) or (
            isinstance(item, Tracker) and not isinstance(roi, Roi)
        ):
            raise ValidationError(
                f"ROI type {type[roi]} does not match tracker type {type[item]}"
            )

        if find_roi(item, roi.uuid, silent=True) is not None:
            raise ROIExistsError(site_id, camera_id, roi.uuid)

        item.rois.append(roi)

        validated_object = CameraTrackerAdapter.validate_python(item.model_dump())

        return update_table_item(
            table=self._context.things_shadow_table,
            item=validated_object,
            site_id=site_id,
            camera_id=get_thing_record_name(site_id, camera_id),
        )

    def update(
        self, site_id: str, camera_id: str, roi_id: str, roi: TrackerRoi
    ) -> CameraTracker:
        item = get_tracker_item(
            table=self._context.things_shadow_table,
            site_id=site_id,
            tracker_id=get_thing_record_name(site_id, camera_id),
        )
        if roi_id != str(roi.uuid):
            return ValidationError("The request UUID and the RoI UUID don't match.")
        # search for the ROI
        roi_index, roi_db = find_roi(item, roi_id)
        roi_merged = recursive_merge_with_null_deletions(
            roi_db.model_dump(mode="json"), roi
        )
        # validating roi object before updating
        roi_merged_validated = TrackerRoiAdapter.validate_python(roi_merged)

        item.rois[roi_index] = roi_merged_validated

        # validating object after updating ROI
        item_object = CameraTrackerAdapter.validate_python(item.model_dump())

        return update_table_item(
            table=self._context.things_shadow_table,
            item=item_object,
            site_id=site_id,
            camera_id=get_thing_record_name(site_id, camera_id),
        )

    def delete(self, site_id: str, camera_id: str, roi_id: str) -> CameraTracker:
        item = get_tracker_item(
            table=self._context.things_shadow_table,
            site_id=site_id,
            tracker_id=get_thing_record_name(site_id, camera_id),
        )
        index, _ = find_roi(item, roi_id)
        del item.rois[index]
        return update_table_item(
            table=self._context.things_shadow_table,
            item=item,
            site_id=site_id,
            camera_id=get_thing_record_name(site_id, camera_id),
        )
