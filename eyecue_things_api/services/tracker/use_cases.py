from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import (
    delete_table_item,
    insert_table_item,
    scan_table_parsed,
    update_table_item,
)
from eyecue_things_api.exceptions.errors import ValidationError
from eyecue_things_api.models.use_cases.use_cases import UseCase
from eyecue_things_api.util import parse_uuid


class UseCasesService:
    def __init__(self, context: Context) -> None:
        self._context: Context = context

    def get_all_use_cases(self) -> list[UseCase]:
        return scan_table_parsed(
            model=UseCase,
            table=self._context.use_cases_table,
        )

    def create(self, use_case: UseCase) -> UseCase:
        use_case = UseCase.model_validate(use_case)
        return insert_table_item(
            table=self._context.use_cases_table,
            item=use_case,
            use_case_id=use_case.use_case_id,
        )

    def update(self, use_case_id: str, use_case: UseCase) -> UseCase:
        if use_case_id != str(use_case.use_case_id):
            return ValidationError("The request UUID and the RoI UUID don't match.")
        use_case = UseCase.model_validate(use_case)
        return update_table_item(
            table=self._context.use_cases_table,
            item=use_case,
            use_case_id=use_case.use_case_id,
        )

    def delete(self, use_case_id: str) -> None:
        parsed_use_case_id = parse_uuid(use_case_id)
        return delete_table_item(
            table=self._context.use_cases_table,
            use_case_id=str(parsed_use_case_id),
        )
