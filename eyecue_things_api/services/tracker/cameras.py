from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import (
    delete_table_item,
    get_tracker_item,
    insert_table_item,
    update_table_item,
)
from eyecue_things_api.exceptions.errors import ValidationError
from eyecue_things_api.models.discriminator import Camera<PERSON><PERSON>, CameraTrackerAdapter
from eyecue_things_api.util import (
    get_thing_record_name,
    recursive_merge_with_null_deletions,
)


class CamerasService:
    def __init__(self, context: Context) -> None:
        self._context: Context = context

    def get(self, site_id: str, camera_id: str) -> CameraTracker:
        return get_tracker_item(
            table=self._context.things_shadow_table,
            site_id=site_id,
            tracker_id=get_thing_record_name(site_id, camera_id),
        )

    def create(self, site_id: str, tracker: CameraTracker) -> CameraTracker:
        if site_id != tracker.site_id:
            return ValidationError("The request UUID and the RoI UUID don't match.")
        return insert_table_item(
            table=self._context.things_shadow_table,
            item=tracker,
            site_id=site_id,
            camera_id=tracker.camera_id,
        )

    def update(
        self, site_id: str, camera_id: str, tracker_config: CameraTracker
    ) -> CameraTracker:
        # getting current item on DynamoDB
        item_db = get_tracker_item(
            table=self._context.things_shadow_table,
            site_id=site_id,
            tracker_id=get_thing_record_name(site_id, camera_id),
        )
        # applying update rules in case of parameters deletion and updates
        item_merged = recursive_merge_with_null_deletions(
            item_db.model_dump(mode="json"), tracker_config
        )

        # validating the tracker configuration object
        validated_object = CameraTrackerAdapter.validate_python(item_merged)

        # saving tracker configuration object after applying the update rules
        return update_table_item(
            table=self._context.things_shadow_table,
            item=validated_object,
            site_id=site_id,
            camera_id=get_thing_record_name(site_id, camera_id),
        )

    def delete(self, site_id: str, camera_id: str) -> None:
        return delete_table_item(
            table=self._context.things_shadow_table,
            site_id=site_id,
            camera_id=get_thing_record_name(site_id, camera_id),
        )
