from typing import Any

from eyecue_things_api.context import Context
from eyecue_things_api.exceptions.errors import ValidationError
from eyecue_things_api.models.tracker.indoor_roi import IndoorRoi
from eyecue_things_api.models.tracker.roi import Roi
from eyecue_things_api.models.tracker.use_cases_rois import UseCasesRois
from eyecue_things_api.models.use_cases.use_cases import UseCase
from eyecue_things_api.models.use_cases.use_cases_installations import (
    UseCaseInstallation,
)


class SchemaService:
    def __init__(self, context: Context) -> None:
        self._context: Context = context
        self._use_cases: type[UseCase] = UseCase
        self._use_cases_rois: UseCasesRois = UseCasesRois()
        self._use_case_installations: type[UseCaseInstallation] = UseCaseInstallation

    def get_roi_schema(self, roi_type: str) -> dict[str, Any]:
        if roi_type.lower() == "drive-thru":
            return Roi.model_json_schema()

        if roi_type.lower() == "indoor":
            return IndoorRoi.model_json_schema()

        raise ValidationError(
            f"ROI type {roi_type} is not implemented, try Drive-Thru or Indoor."
        )

    def get_use_case_roi_schema(self, use_case: str) -> list[dict[str, Any]]:
        schema: list[dict[str, Any]] | None = self._use_cases_rois.get_schema(use_case)
        if schema is None:
            raise ValidationError(
                f"The requested use case {use_case} is not implemented."
            )
        return schema

    def get_use_case_schema(self) -> dict[str, Any]:
        return self._use_cases.model_json_schema()

    def get_use_case_installation_schema(self) -> dict[str, Any]:
        return self._use_case_installations.model_json_schema()
