from http import HTTPStatus


class ServiceError(Exception):
    status_code: HTTPStatus

    def __init_subclass__(cls, *, status_code: HTTPStatus, **kwargs) -> None:
        cls.status_code = status_code
        super().__init_subclass__(**kwargs)

    def __init__(self, message: str | None = None) -> None:
        super().__init__(message or self.__class__.__name__)


class NotFoundError(ServiceError, status_code=HTTPStatus.NOT_FOUND): ...


class BadRequestError(ServiceError, status_code=HTTPStatus.BAD_REQUEST): ...


class ConflictError(ServiceError, status_code=HTTPStatus.CONFLICT): ...


class ForbiddenError(ServiceError, status_code=HTTPStatus.FORBIDDEN): ...


class InternalServerError(
    ServiceError, status_code=HTTPStatus.INTERNAL_SERVER_ERROR
): ...


class ModelValidationError(
    ServiceError, status_code=HTTPStatus.UNPROCESSABLE_ENTITY
): ...


class ValidationError(ServiceError, status_code=HTTPStatus.UNPROCESSABLE_ENTITY): ...


HANDLED_ERRORS: list[ServiceError] = [
    NotFoundError,
    BadRequestError,
    ConflictError,
    ForbiddenError,
    InternalServerError,
    ValidationError,
    ModelValidationError,
]
