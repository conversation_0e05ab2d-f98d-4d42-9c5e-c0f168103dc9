from collections.abc import Iterable
from datetime import UTC, datetime
from typing import Any, TypeVar, Unpack

from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
from mypy_boto3_dynamodb.service_resource import Table
from mypy_boto3_dynamodb.type_defs import (
    QueryInputTableQueryTypeDef,
    ScanInputTableScanTypeDef,
)
from pydantic import BaseModel, TypeAdapter

from eyecue_things_api.boto3_type_defs import (
    GetItemInputTableGetItemTypeDef,
)
from eyecue_things_api.models import Server, ThingShadowDiscriminator, Tracker
from eyecue_things_api.models.discriminator import CameraTracker
from eyecue_things_api.models.exceptions import (
    ItemDeleteError,
    ItemExistsError,
    ItemNotFoundError,
    ItemUpdateError,
    ModelTypeError,
)
from eyecue_things_api.models.tracker.indoor_tracker import IndoorTracker
from eyecue_things_api.models.types import ServerId, SiteId, ThingId, TrackerId
from eyecue_things_api.util import convert_floats_to_decimal, get_keys_from_table

T = TypeVar("T", bound=ThingShadowDiscriminator)
adapter: TypeAdapter[ThingShadowDiscriminator] = TypeAdapter(ThingShadowDiscriminator)
GenericT = TypeVar("GenericT", bound=BaseModel)


def query_table(
    table: Table,
    **kwargs: Unpack[QueryInputTableQueryTypeDef],
) -> Iterable[dict[str, Any]]:
    """Query a DynamoDB table and return all items.

    Args:
        table (Table): The DynamoDB table to query
        **kwargs: Additional arguments to pass to the query method

    Returns:
        Iterable[dict[str, Any]]: An iterable of items in the table

    """
    response = table.query(**kwargs)
    yield from response["Items"]

    while "LastEvaluatedKey" in response:
        kwargs["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        response = table.query(**kwargs)
        yield from response["Items"]


def query_table_parsed(
    model: type[GenericT],
    table: Table,
    **kwargs: dict[str, Any],
) -> list[GenericT]:
    """Query a DynamoDB table and return all items, parsed as ThingShadowDiscriminator.

    Args:
        model(type[GenericT]): The Pydantic model to validate the table items.
        table (Table): The DynamoDB table to query
        **kwargs: Additional arguments to pass to the query method

    Returns:
        Iterable[GenericT]: An iterable of items in the table.

    """
    table_keys = get_keys_from_table(table, include_keys_types=True)
    params_values = {k: kwargs.pop(k) for k in table_keys if k in kwargs}
    if params_values:
        key_condition = Key(table_keys["HASH"]).eq(params_values[table_keys["HASH"]])
        if (
            table_keys["RANGE"] in params_values
            and params_values[table_keys["RANGE"]] is not None
        ):
            key_condition &= Key(table_keys["RANGE"]).begins_with(
                params_values[table_keys["RANGE"]]
            )
        kwargs["KeyConditionExpression"] = key_condition

    model_adapter = TypeAdapter(model)
    return [
        model_adapter.validate_python(item) for item in query_table(table, **kwargs)
    ]


def scan_table(
    table: Table,
    **kwargs: Unpack[ScanInputTableScanTypeDef],
) -> Iterable[dict[str, Any]]:
    """Scan a DynamoDB table and return all items.

    Args:
        table (Table): The DynamoDB table to scan
        **kwargs: Additional arguments to pass to the scan method

    Returns:
        Iterable[dict[str, Any]]: An iterable of items in the table

    """
    response = table.scan(**kwargs)
    yield from response["Items"]

    while "LastEvaluatedKey" in response:
        kwargs["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        response = table.scan(**kwargs)
        yield from response["Items"]


def scan_table_parsed(
    model: type[GenericT],
    table: Table,
    **kwargs: Unpack[ScanInputTableScanTypeDef],
) -> list[GenericT]:
    """Scan a DynamoDB table and return all items, parsed as GenericT.

    Args:
        model(type[GenericT]): The Pydantic model to validate the table items.
        table (Table): The DynamoDB table to scan
        **kwargs: Additional arguments to pass to the scan method

    Returns:
        Iterable[GenericT]: An iterable of items in the table

    """
    model_adapter = TypeAdapter(model)
    return [model_adapter.validate_python(item) for item in scan_table(table, **kwargs)]


def get_table_item(
    table: Table,
    site_id: SiteId,
    thing_id: ThingId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> dict[str, Any]:
    """Get a single item from a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        thing_id (ThindId): The thing_id (camera_id in DynamoDB) of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        dict[str, Any]: The item from the table

    Raises:
        ItemNotFoundError: If the item is not found

    """
    response = table.get_item(
        Key={
            "site_id": site_id,
            "camera_id": thing_id,
        },
        **kwargs,
    )
    try:
        return response["Item"]
    except KeyError as e:
        raise ItemNotFoundError(site_id, thing_id) from e


def get_table_item_parsed(
    table: Table,
    site_id: SiteId,
    thing_id: ThingId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> ThingShadowDiscriminator:
    """Get a single item from the DynamoDB table, parsed as ThingShadowDiscriminator.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        thing_id (ThingId): The thing_id (camera_id in DynamoDB) of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        ThingShadowDiscriminator: The item from the table

    Raises:
        ItemNotFoundError: If the item does not exist

    """
    return adapter.validate_python(get_table_item(table, site_id, thing_id, **kwargs))


def get_server_item(
    table: Table,
    site_id: SiteId,
    server_id: ServerId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> Server:
    """Get a Server item from the DynamoDB table.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        server_id (ServerId): The server_id of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        Server: The Server item

    Raises:
        ModelTypeError: If the item is not of type Server
        ItemNotFoundError: If the item does not exist

    """
    response = get_table_item_parsed(
        table,
        site_id,
        server_id,
        **kwargs,
    )

    if not isinstance(response, Server):
        raise ModelTypeError(Server, type(response))

    return response


def get_tracker_item(
    table: Table,
    site_id: SiteId,
    tracker_id: TrackerId,
    **kwargs: Unpack[GetItemInputTableGetItemTypeDef],
) -> CameraTracker:
    """Get a Tracker item from the DynamoDB table.

    Args:
        table (Table): The DynamoDB table to get the item from
        site_id (SiteId): The site_id of the item
        tracker_id (TrackerId): The tracker_id of the item
        **kwargs: Additional arguments to pass to the get_item method

    Returns:
        CameraTracker: The Tracker item

    Raises:
        ModelTypeError: If the item is not of type Tracker
        ItemNotFoundError: If the item does not exist

    """
    response = get_table_item_parsed(
        table,
        site_id,
        tracker_id,
        **kwargs,
    )

    if not isinstance(response, Tracker) and not isinstance(response, IndoorTracker):
        raise ModelTypeError([Tracker, IndoorTracker], type(response))

    return response


def insert_table_item(
    table: Table,
    item: GenericT,
    **kwargs: dict[str, Any],
) -> GenericT:
    """Insert an item into a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to insert the item into
        item (ThingShadowDiscriminator): The item to insert
        **kwargs (dict[str, Any]): Additional arguments
            to pass to the put_item method

    Returns:
        ThingShadowDiscriminator: The inserted item.

    Raises:
        ItemExistsError: If the item already exists in the database

    """
    table_keys = get_keys_from_table(table)
    key_values = {k: kwargs.pop(k) for k in table_keys if k in kwargs}

    if table_keys:
        condition_expressions = [f"attribute_not_exists({key})" for key in table_keys]
        kwargs["ConditionExpression"] = " AND ".join(condition_expressions)
    try:
        item.last_updated_timestamp = datetime.now(UTC)
        item_json = item.model_dump(mode="json")
        item_json = convert_floats_to_decimal(item_json)
        table.put_item(Item=item_json, **kwargs)
    except ClientError as e:
        if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
            raise ItemExistsError(**key_values) from e
        raise e

    return item


def update_table_item(
    table: Table,
    item: GenericT,
    **kwargs: dict[str, Any],
) -> GenericT:
    """Update an item in a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to update the item in
        item (ThingShadowDiscriminator): The item to update
        **kwargs (dict[str, Any]): Additional arguments
            to pass to the put_item method

    Returns:
        ThingShadowDiscriminator: The updated item

    Raises:
        ItemUpdateError: If the item has been updated since it was last read or
            if the item does not exist

    """
    table_keys = get_keys_from_table(table)
    if table_keys:
        condition_expressions = [f"attribute_exists({key})" for key in table_keys]
        kwargs["ConditionExpression"] = " AND ".join(condition_expressions)
    if hasattr(item, "last_updated_timestamp"):
        last_updated_timestamp_before = item.last_updated_timestamp
        kwargs["ConditionExpression"] += (
            "and last_updated_timestamp = :updated_timestamp"
        )
        kwargs["ExpressionAttributeValues"] = {
            ":updated_timestamp": last_updated_timestamp_before.isoformat(),
        }
    key_values = {k: kwargs.pop(k) for k in table_keys if k in kwargs}
    if len(key_values) != len(table_keys):
        raise ValueError(f"Missing key values for: {set(table_keys) - set(key_values)}")
    try:
        item.last_updated_timestamp = datetime.now(UTC)
        item_json = item.model_dump(mode="json")
        item_json = convert_floats_to_decimal(item_json)
        table.put_item(Item=item_json, **kwargs)
    except ClientError as e:
        if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
            raise ItemUpdateError(**key_values) from e

        raise e

    return item


def delete_table_item(
    table: Table,
    **kwargs: dict[str, Any],
) -> None:
    """Delete an item from a DynamoDB table.

    Args:
        table (Table): The DynamoDB table to delete the item from
        **kwargs: Additional arguments to pass to the delete_item method

    Raises:
        ItemDeleteError: If the item has already been deleted

    """
    table_keys = get_keys_from_table(table)
    if table_keys:
        condition_expressions = [f"attribute_exists({key})" for key in table_keys]
        kwargs["ConditionExpression"] = " AND ".join(condition_expressions)
    key_values = {k: kwargs.pop(k) for k in table_keys if k in kwargs}
    if len(key_values) != len(table_keys):
        raise ValueError(f"Missing key values for: {set(table_keys) - set(key_values)}")
    try:
        table.delete_item(
            Key=key_values,
            **kwargs,
        )
    except ClientError as e:
        if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
            raise ItemDeleteError(**key_values) from e
        raise e
