from eyecue_things_api.api_gateway_sub_router import APIGatewaySubRouter
from eyecue_things_api.routes.cameras import router as cameras_router
from eyecue_things_api.routes.rois import router as rois_router
from eyecue_things_api.routes.schemas import (
    router as schemas_router,
)
from eyecue_things_api.routes.use_cases import (
    router as use_cases_router,
)
from eyecue_things_api.routes.use_cases_installation import (
    router as use_cases_installation_router,
)

router = APIGatewaySubRouter()

router.include_router(cameras_router)
router.include_router(rois_router)
router.include_router(use_cases_router)
router.include_router(use_cases_installation_router)
router.include_router(schemas_router)
