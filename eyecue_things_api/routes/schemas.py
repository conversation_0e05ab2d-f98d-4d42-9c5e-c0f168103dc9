from http import HTTPStatus
from typing import TYPE_CHECKING, Annotated, Any, cast

from aws_lambda_powertools.event_handler import Response
from aws_lambda_powertools.event_handler.openapi.params import Path
from aws_lambda_powertools.event_handler.router import APIGatewayRouter

from eyecue_things_api.context import Context
from eyecue_things_api.response import SuccessResponse
from eyecue_things_api.services.tracker.schemas import SchemaService

if TYPE_CHECKING:
    from aws_lambda_powertools.event_handler.openapi.types import OpenAPIResponse

router = APIGatewayRouter()
schema_service = SchemaService(Context())

responses = cast(
    "dict[int, OpenAPIResponse]",
    {
        HTTPStatus.OK: {"description": "Success"},
        HTTPStatus.NOT_FOUND: {"description": "Not Found"},
        HTTPStatus.FORBIDDEN: {"description": "Forbidden"},
        HTTPStatus.INTERNAL_SERVER_ERROR: {"description": "Server Error"},
    },
)


@router.get(
    rule="/org/<organisation>/schemas/rois/<roi_type>",
    tags=["Schemas"],
    summary="Get JSON Schema for RoIs",
    operation_id="getRoISchema",
    responses=responses,
)
def get_roi_schema(
    organisation: Annotated[str, Path(description="The organisation id.")],
    roi_type: Annotated[
        str, Path(description="The site id of the store. Ex.: Indoor, Drive-Thru.")
    ],
) -> Response[dict[str, Any]]:
    roi_schema = schema_service.get_roi_schema(roi_type)
    return SuccessResponse[dict[str, Any]](roi_schema)


@router.get(
    rule="/org/<organisation>/schemas/use-case-rois/<use_case_id>",
    tags=["Schemas"],
    summary="Get RoI JSON Schema for the requested use case",
    operation_id="getUseCaseRoiSchema",
    responses=responses,
)
def get_use_case_roi_schema(
    organisation: Annotated[str, Path(description="The organisation id.")],
    use_case_id: Annotated[
        str, Path(description="The RoI JSON Schema for the use case.")
    ],
) -> Response[list[dict[str, Any]]]:
    schema = schema_service.get_use_case_roi_schema(use_case_id)
    return SuccessResponse[list[dict[str, Any]]](schema)


@router.get(
    rule="/org/<organisation>/schemas/use-cases",
    tags=["Schemas"],
    summary="Get JSON Schema for a use case.",
    operation_id="getUseCaseSchema",
    responses=responses,
)
def get_use_cases_schema(
    organisation: Annotated[str, Path(description="The organisation id.")],
) -> Response[dict[str, Any]]:
    schema = schema_service.get_use_case_schema()
    return SuccessResponse[dict[str, Any]](schema)


@router.get(
    rule="/org/<organisation>/schemas/installations",
    tags=["Schemas"],
    summary="Get JSON Schema for the use cases installations.",
    operation_id="getUseCaseInstallationSchema",
    responses=responses,
)
def get_use_case_installations_schema(
    organisation: Annotated[str, Path(description="The organisation id.")],
) -> Response[dict[str, Any]]:
    schema: dict[str, Any] = schema_service.get_use_case_installation_schema()
    return SuccessResponse[dict[str, Any]](schema)
