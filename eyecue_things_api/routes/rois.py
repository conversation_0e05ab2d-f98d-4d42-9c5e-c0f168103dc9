from http import HTTPStatus
from typing import TYPE_CHECKING, Annotated, cast

from aws_lambda_powertools.event_handler import Response
from aws_lambda_powertools.event_handler.openapi.params import Body, Path
from aws_lambda_powertools.event_handler.router import APIGatewayRouter

from eyecue_things_api.context import Context
from eyecue_things_api.models.discriminator import Camera<PERSON>racker, TrackerRoi
from eyecue_things_api.response import SuccessResponse
from eyecue_things_api.services.tracker.rois import RoiService

if TYPE_CHECKING:
    from aws_lambda_powertools.event_handler.openapi.types import OpenAPIResponse

router = APIGatewayRouter()
roi_service = RoiService(Context())

responses = cast(
    "dict[int, OpenAPIResponse]",
    {
        HTTPStatus.OK: {"description": "Success"},
        HTTPStatus.NOT_FOUND: {"description": "Not Found"},
        HTTPStatus.FORBIDDEN: {"description": "Forbidden"},
        HTTPStatus.INTERNAL_SERVER_ERROR: {"description": "Server Error"},
    },
)


@router.get(
    rule="/org/<organisation>/sites/<site_id>/cameras/<camera_id>/rois/<roi_id>",
    tags=["Rois"],
    summary="Get RoI",
    operation_id="getRoi",
    responses=responses,
)
def get_roi(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store")],
    camera_id: Annotated[
        str, Path(description="The camera id as numbers (example: 019)")
    ],
    roi_id: Annotated[str, Path(description="The UUID of th RoI")],
) -> Response[TrackerRoi]:
    item = roi_service.get(site_id, camera_id, roi_id)
    return SuccessResponse[TrackerRoi](item)


@router.post(
    rule="/org/<organisation>/sites/<site_id>/cameras/<camera_id>/rois",
    tags=["Rois"],
    summary="Create RoI for tracker",
    operation_id="createRoI",
    responses=responses,
)
def create_roi(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store")],
    camera_id: Annotated[
        str, Path(description="The camera id as numbers (example: 019)")
    ],
    roi: Annotated[TrackerRoi, Body(description="Tracker RoI payload")],
) -> Response[CameraTracker]:
    item = roi_service.create(site_id, camera_id, roi)
    return SuccessResponse[CameraTracker](item)


@router.put(
    rule="/org/<organisation>/sites/<site_id>/cameras/<camera_id>/rois/<roi_id>",
    tags=["Rois"],
    summary="Update RoI for tracker",
    operation_id="updateRoI",
    responses=responses,
)
def update_roi(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store")],
    camera_id: Annotated[
        str, Path(description="The camera id as numbers (example: 019)")
    ],
    roi_id: Annotated[str, Path(description="The UUID of th RoI")],
    roi: Annotated[TrackerRoi, Body(description="Tracker RoI payload")],
) -> Response[CameraTracker]:
    item = roi_service.update(site_id, camera_id, roi_id, roi)
    return SuccessResponse[CameraTracker](item)


@router.delete(
    rule="/org/<organisation>/sites/<site_id>/cameras/<camera_id>/rois/<roi_id>",
    tags=["Rois"],
    summary="Delete RoI for tracker",
    operation_id="deleteRoI",
    responses=responses,
)
def delete_roi(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store")],
    camera_id: Annotated[
        str, Path(description="The camera id as numbers (example: 019)")
    ],
    roi_id: Annotated[str, Path(description="The UUID of th RoI")],
) -> Response[CameraTracker]:
    item = roi_service.delete(site_id, camera_id, roi_id)
    return SuccessResponse[CameraTracker](item)
