import os
from collections.abc import Callable
from http import HTT<PERSON>tat<PERSON>
from typing import Any

from aws_lambda_powertools.middleware_factory import lambda_handler_decorator
from aws_lambda_powertools.utilities.parser import parse
from aws_lambda_powertools.utilities.parser.types import T
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import ValidationError

from eyecue_things_api.response import (
    LambdaResponse,
    ValidationErrorBody,
    ValidationErrorResponse,
)
from eyecue_things_api.util import exception_handler, format_validation_error


@lambda_handler_decorator
def validated_event_parser(
    handler: Callable[..., LambdaResponse],
    event: dict[str, Any],
    context: LambdaContext,
    model: type[T],
    **kwargs: Any,
) -> dict[str, Any] | LambdaResponse:
    """Event parser with custom exception handling.

    This function wraps around the `parse` function from the AWS Lambda
    Powertools library, providing custom exception handling for validation errors.

    It is intended to be used as a decorator for Lambda handlers so that if the event
    fails validation, a `ValidationErrorResponse` is returned with a 422 status code.

    """
    try:
        parsed_event: T = parse(model=model, event=event)
        try:
            response = handler(parsed_event, context, **kwargs)
        except Exception as e:  # noqa: BLE001
            response = exception_handler(e)
    except ValidationError as e:
        # When the incoming event fails validation, return a 422 response
        message = format_validation_error(e)
        response = ValidationErrorResponse(
            status_code=HTTPStatus.UNPROCESSABLE_ENTITY.value,
            body=ValidationErrorBody(
                error=HTTPStatus.UNPROCESSABLE_ENTITY.phrase,
                message=message,
                details=e.errors(),
            ),
        )

    # If running in a Lambda environment, return the response as a dictionary
    # Otherwise, return the LambdaResponse object.
    if "LAMBDA_TASK_ROOT" in os.environ:
        return response.model_dump(mode="json")
    return response
