from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from boto3.dynamodb.conditions import Key
from pydantic import BaseModel

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import query_table_parsed
from eyecue_things_api.middleware import validated_event_parser
from eyecue_things_api.model_config import model_config
from eyecue_things_api.models.tracker.indoor_tracker import IndoorTracker
from eyecue_things_api.models.types import SiteId
from eyecue_things_api.response import LambdaResponse

logger = Logger()


class IndoorTrackerListEvent(BaseModel):
    model_config = model_config

    site_id: SiteId


class IndoorTrackerListResponse(LambdaResponse):
    body: list[IndoorTracker]


@logger.inject_lambda_context
@validated_event_parser(model=IndoorTrackerListEvent)  # type: ignore[misc]
def lambda_handler(
    event: IndoorTrackerListEvent,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> LambdaResponse:
    execution_context = execution_context or Context()

    items = query_table_parsed(
        table=execution_context.things_shadow_table,
        KeyConditionExpression=Key("site_id").eq(event.site_id),
    )

    tracker_items = [i for i in items if isinstance(i, IndoorTracker)]
    return IndoorTrackerListResponse(
        status_code=HTTPStatus.OK,
        body=tracker_items,
    )
