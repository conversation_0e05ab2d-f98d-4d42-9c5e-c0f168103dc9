from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import get_indoor_tracker_item
from eyecue_things_api.middleware import validated_event_parser
from eyecue_things_api.models.tracker import Camera
from eyecue_things_api.response import LambdaResponse
from functions.tracker.tracker_get import TrackerGetEvent

logger = Logger()


class IndoorCameraGetEvent(TrackerGetEvent): ...


class IndoorCameraGetResponse(LambdaResponse):
    body: Camera


@logger.inject_lambda_context
@validated_event_parser(model=IndoorCameraGetEvent)  # type: ignore[misc]
def lambda_handler(
    event: IndoorCameraGetEvent,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> LambdaResponse:
    execution_context = execution_context or Context()

    item = get_indoor_tracker_item(
        table=execution_context.things_shadow_table,
        site_id=event.site_id,
        tracker_id=event.tracker_id,
    )

    return IndoorCameraGetResponse(
        status_code=HTTPStatus.OK,
        body=item.camera,
    )
