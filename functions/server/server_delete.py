from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import BaseModel

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import delete_table_item
from eyecue_things_api.middleware import (
    validated_event_parser,
)
from eyecue_things_api.model_config import model_config
from eyecue_things_api.models.types import ServerId, SiteId
from eyecue_things_api.response import LambdaResponse

logger = Logger()


class ServerDeleteEvent(BaseModel):
    model_config = model_config

    site_id: SiteId
    server_id: ServerId


class ServerDeleteResponse(LambdaResponse): ...


@logger.inject_lambda_context
@validated_event_parser(model=ServerDeleteEvent)  # type: ignore[misc]
def lambda_handler(
    event: ServerDeleteEvent,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> LambdaResponse:
    execution_context = execution_context or Context()

    delete_table_item(
        table=execution_context.things_shadow_table,
        site_id=event.site_id,
        thing_id=event.server_id,
    )

    return ServerDeleteResponse(
        status_code=HTTPStatus.NO_CONTENT,
        body=None,
    )
