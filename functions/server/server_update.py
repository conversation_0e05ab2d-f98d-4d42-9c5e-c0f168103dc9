# mypy: ignore-errors
import json
from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import get_server_item, update_table_item
from eyecue_things_api.models import Server
from eyecue_things_api.util import recursive_merge_with_null_deletions

logger = Logger()


@logger.inject_lambda_context
def lambda_handler(
    event: dict,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> dict:
    execution_context = execution_context or Context()

    # getting current item on DynamoDB
    item_db = get_server_item(
        table=execution_context.things_shadow_table,
        site_id=event["site_id"],
        server_id=event["camera_id"],
    )
    # applying update rules in case of parameters deletion and updates
    item_merged = recursive_merge_with_null_deletions(
        item_db.model_dump(mode="json"), event
    )

    # creating a server configuration object
    item_object = Server.model_validate(item_merged)

    # saving server configuration object after applying the update rules
    item = update_table_item(
        table=execution_context.things_shadow_table,
        item=item_object,
    )

    return {
        "status_code": HTTPStatus.OK,
        "body": json.loads(item.model_dump_json()),
    }
