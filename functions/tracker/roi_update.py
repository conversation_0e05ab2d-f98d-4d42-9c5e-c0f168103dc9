# mypy: ignore-errors
import json
import uuid as uuid_lib
from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import get_tracker_item, update_table_item
from eyecue_things_api.models import Tracker
from eyecue_things_api.models.tracker.roi import Roi
from eyecue_things_api.response import LambdaResponse
from eyecue_things_api.util import find_roi, recursive_merge_with_null_deletions

logger = Logger()


@logger.inject_lambda_context
def lambda_handler(
    event: dict,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> LambdaResponse:
    execution_context = execution_context or Context()
    # getting the tracker to add ROI
    item = get_tracker_item(
        table=execution_context.things_shadow_table,
        site_id=event["site_id"],
        tracker_id=event["tracker_id"],
    )

    # search for the ROI
    index, roi_db = find_roi(item, uuid_lib.UUID(event["roi"]["uuid"]))
    roi_merged = recursive_merge_with_null_deletions(
        roi_db.model_dump(mode="json"), event["roi"]
    )
    # validating roi object before updating
    roi_merged_object = Roi.model_validate(roi_merged)
    # updating roi
    item.rois[index] = roi_merged_object

    # validating tracker object after updating ROI
    item_json = item.model_dump(mode="json")
    item_object = Tracker.model_validate(item_json)

    # updating dynamoDB
    item = update_table_item(
        table=execution_context.things_shadow_table,
        item=item_object,
    )

    return {
        "status_code": HTTPStatus.OK,
        "body": json.loads(item.model_dump_json()),
    }
