from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import BaseModel

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import get_tracker_item, update_table_item
from eyecue_things_api.middleware import validated_event_parser
from eyecue_things_api.model_config import model_config
from eyecue_things_api.models import Tracker
from eyecue_things_api.models.tracker.roi import Roi
from eyecue_things_api.models.types import SiteId, TrackerId
from eyecue_things_api.response import LambdaResponse

logger = Logger()


class ROICreateEvent(BaseModel):
    model_config = model_config

    site_id: SiteId
    tracker_id: TrackerId
    roi: Roi


class ROICreateResponse(LambdaResponse):
    body: Tracker


@logger.inject_lambda_context
@validated_event_parser(model=ROICreateEvent)  # type: ignore[misc]
def lambda_handler(
    event: ROICreateEvent,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> LambdaResponse:
    execution_context = execution_context or Context()
    # getting the tracker to add ROI
    item = get_tracker_item(
        table=execution_context.things_shadow_table,
        site_id=event.site_id,
        tracker_id=event.tracker_id,
    )
    # adding ROI
    item.rois.append(event.roi)

    # validating object after adding ROI
    item_json = item.model_dump(mode="json")
    item_object = Tracker.model_validate(item_json)

    # updating dynamoDB
    item_response = update_table_item(
        table=execution_context.things_shadow_table,
        item=item_object,
    )

    return ROICreateResponse(
        status_code=HTTPStatus.OK,
        body=item_response,
    )
