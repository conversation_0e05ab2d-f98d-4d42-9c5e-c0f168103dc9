from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import insert_table_item
from eyecue_things_api.middleware import (
    validated_event_parser,
)
from eyecue_things_api.models import Tracker
from eyecue_things_api.response import LambdaResponse

logger = Logger()


class TrackerCreateEvent(Tracker): ...


class TrackerCreateResponse(LambdaResponse):
    body: Tracker


@logger.inject_lambda_context
@validated_event_parser(model=TrackerCreateEvent)  # type: ignore[misc]
def lambda_handler(
    event: TrackerCreateEvent,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> LambdaResponse:
    execution_context = execution_context or Context()

    item = insert_table_item(
        table=execution_context.things_shadow_table,
        item=event,
    )

    return TrackerCreateResponse(
        status_code=HTTPStatus.CREATED,
        body=item,
    )
