from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.models.tracker.tracker import Tracker

logger = Logger()


@logger.inject_lambda_context
# mypy: ignore-errors
def lambda_handler(
    event,  # noqa: ANN001, ARG001
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> dict:
    execution_context = execution_context or Context()

    item = Tracker.model_json_schema()

    return {
        "status_code": HTTPStatus.OK,
        "body": item,
    }
