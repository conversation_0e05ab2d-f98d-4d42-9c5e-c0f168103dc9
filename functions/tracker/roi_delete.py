import uuid as uuid_lib
from http import HTTPStatus

from aws_lambda_powertools.logging import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import BaseModel

from eyecue_things_api.context import Context
from eyecue_things_api.dynamodb import get_tracker_item, update_table_item
from eyecue_things_api.middleware import validated_event_parser
from eyecue_things_api.model_config import model_config
from eyecue_things_api.models import Tracker
from eyecue_things_api.models.types import SiteId, TrackerId
from eyecue_things_api.response import LambdaResponse
from eyecue_things_api.util import find_roi

logger = Logger()


class ROIDeleteEvent(BaseModel):
    model_config = model_config

    site_id: SiteId
    tracker_id: TrackerId
    roi_uuid: uuid_lib.UUID


class ROIDeleteResponse(LambdaResponse):
    body: Tracker


@logger.inject_lambda_context
@validated_event_parser(model=ROIDeleteEvent)  # type: ignore[misc]
def lambda_handler(
    event: R<PERSON><PERSON><PERSON>teEvent,
    context: LambdaContext,  # noqa: ARG001
    execution_context: Context | None = None,
) -> LambdaResponse:
    execution_context = execution_context or Context()
    # getting the tracker to add ROI
    item = get_tracker_item(
        table=execution_context.things_shadow_table,
        site_id=event.site_id,
        tracker_id=event.tracker_id,
    )

    # search for the ROI
    index, _ = find_roi(item, event.roi_uuid)

    # removing ROI
    del item.rois[index]

    # updating dynamoDB
    item_response = update_table_item(
        table=execution_context.things_shadow_table,
        item=item,
    )

    return ROIDeleteResponse(
        status_code=HTTPStatus.OK,
        body=item_response,
    )
