from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.utilities.data_classes.api_gateway_authorizer_event import APIGatewayAuthorizerRequestEvent

from src.config import Config
from src.jwt import <PERSON><PERSON><PERSON>uth<PERSON><PERSON>, JWTClaims
from src.policy import generate_policy

logger = Logger()
config = Config()


def _build_allowed_resources(method_arn: str, claims: JWTClaims) -> list[str]:
    """Build a list of allowed execute-api ARNs for organisations and sites.

    Rules:
    - Only allow resources under /api/v1/org/{organisation}/...
    - If org has wildcard access ("*"), allow all under that org
    - Otherwise, allow only site endpoints for explicit sites, plus org-level endpoints
      that are not under /sites
    """
    # methodArn format: arn:aws:execute-api:{region}:{account}:{apiId}/{stage}/{verb}/{resourcePath}
    arn_parts = method_arn.split("/")
    if len(arn_parts) < 2:
        return []  # Unexpected format

    arn_base = arn_parts[0]  # arn:aws:execute-api:region:account:apiId

    # Determine allowed organisations from claims
    sites_map = claims.app_metadata.iq_resources.sites or {}

    allowed_resources: list[str] = []
    for org, site_list in sites_map.items():
        if "*" in site_list:
            # Full access to everything under the organisation
            allowed_resources.append(f"{arn_base}/*/*/api/v1/org/{org}/*")
            allowed_resources.append(f"{arn_base}/*/*/api/v1/org/{org}")
        else:
            # Org-level (non-site) endpoints explicitly allowed
            allowed_resources.extend(
                [
                    f"{arn_base}/*/*/api/v1/org/{org}",
                    f"{arn_base}/*/*/api/v1/org/{org}/schemas/*",
                    f"{arn_base}/*/*/api/v1/org/{org}/use-cases",
                    f"{arn_base}/*/*/api/v1/org/{org}/use-cases/*",
                    f"{arn_base}/*/*/api/v1/org/{org}/installations",
                    f"{arn_base}/*/*/api/v1/org/{org}/installations/*",
                ]
            )

            # Site-specific endpoints
            for site_id in site_list:
                allowed_resources.append(
                    f"{arn_base}/*/*/api/v1/org/{org}/sites/{site_id}/*"
                )

    return allowed_resources


@logger.inject_lambda_context
def lambda_handler(event: APIGatewayAuthorizerRequestEvent, context: LambdaContext) -> dict | None:
    """Lambda handler for HTTP API (v2) request authorizer.

    Rules:
    - Only allow routes that match /api/v1/org/{organisation}/...
    - Only for organisations/sites the user has access to (from JWT claims via policy scoping)
    - Build a policy listing all org/site combinations the user can access
    """
    try:
        # Extract token from common event shapes
        token = event.authorization_token.replace("Bearer ", "").strip()

        if not token:
            logger.error("No authorization token provided")
            raise ValueError("No authorization token provided")

        if not event.method_arn:
            logger.error("No route/method ARN provided")
            raise ValueError("No route/method ARN provided")

        # Initialize authorizer
        authorizer = JWTAuthorizer(issuer=config.auth0_domain, audience=config.auth0_audience)

        # Validate token and extract claims
        claims = authorizer.validate_token(token)

        # Build list of allowed resources based on organisations + sites in claims
        allowed_resources = _build_allowed_resources(event.method_arn, claims)
        if not allowed_resources:
            # No accessible organisations -> deny the invoked method only
            return generate_policy("unauthorized", "Deny", event.method_arn)

        # Return an allow policy for the computed resource ARNs
        return generate_policy(claims.sub, "Allow", allowed_resources)

    except Exception as e:
        logger.error("Authorization failed", extra={"error": str(e)})
        return generate_policy("unauthorized", "Deny", event.method_arn)
