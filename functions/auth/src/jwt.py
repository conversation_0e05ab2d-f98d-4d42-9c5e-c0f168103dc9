import jwt
from jwt import PyJWKClient
from aws_lambda_powertools import Logger

from pydantic import BaseModel, Field

logger = Logger()


class IQResources(BaseModel):
    roles: list[str]
    sites: dict[str, list[str]]


class AppMetadata(BaseModel):
    allowed_sites: list[int]
    cognito_roles: list[str]
    iq_data: dict[str, dict[str, dict[str, list[str]]]]
    iq_resources: IQResources
    organization: str
    welcome_email_sent: bool


class JWTClaims(BaseModel):
    sub: str
    email: str
    roles: list[str]
    allowed_sites: list[str]
    organization: str
    app_metadata: AppMetadata = Field(alias='https://www.fingermark.tech/databoard/app_metadata')


class JWTAuthorizer:
    def __init__(self, issuer: str, audience: str | list[str]):
        self.issuer = issuer
        self.audience = audience
        # Auth0 JWKS lives under /.well-known
        self.jwks_client = PyJWKClient(f"{self.issuer.rstrip('/')}/.well-known/jwks.json")

    def validate_token(self, token: str) -> JWTClaims:
        """Validate JWT token and return decoded claims."""
        try:
            # Get the signing key from Auth0
            signing_key = self.jwks_client.get_signing_key_from_jwt(token)

            # Decode and validate the token
            decoded_token = jwt.decode(
                token,
                signing_key.key,
                algorithms=["RS256"],
                audience=self.audience,
                issuer=self.issuer,
                options={"verify_exp": True}
            )

            logger.info("JWT token validated successfully", extra={"sub": decoded_token.get("sub")})
            return JWTClaims(**decoded_token)

        except jwt.ExpiredSignatureError:
            logger.error("JWT token has expired")
            raise ValueError("Token has expired")
        except jwt.InvalidTokenError as e:
            logger.error("Invalid JWT token", extra={"error": str(e)})
            raise ValueError(f"Invalid token: {str(e)}")
        except Exception as e:
            logger.error("Unexpected error validating JWT", extra={"error": str(e)})
            raise ValueError(f"Token validation failed: {str(e)}")
