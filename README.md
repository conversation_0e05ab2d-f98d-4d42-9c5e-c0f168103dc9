# Eyecue Things API

This is the AWS Lambda API for the [Eyecue Things](https://bitbucket.org/fingermarkltd/eyecue-things/src/master/) project.

The API is written in Python 3.12 and uses the [Serverless Framework](https://serverless.com/) to deploy to AWS.

You can find the complete documentation and a description of all Lambda functions on this [Notion page](https://www.notion.so/fingermark/Eyecue-Things-API-Documentation-1dec033e5b548009bbbfefe73ea88a43).

## Background

The Eyecue Things project consists of a DynamoDB table called `eyecue-things-shadow` which stores the state of all the things (`eyecue-tracker` & `eyecue-server`) in the fleet. This API is used to interact with this table.

When an item is added/updated in the `eyecue-things-shadow` table, there is a lambda which is triggered to update the state of the Thing, which is subsequently reloaded in either the `eyecue-tracker` or `eyecue-server` on the server.

## Getting Started

First, clone the repository:

```bash
git clone https://bitbucket.org/fingermarkltd/eyecue-things-api.git
cd eyecue-things-api
```

Then, create a virtual environment and install the dependencies:

```bash
# With virtualenv
python3 -m venv venv
source venv/bin/activate

# With pyenv
pyenv virtualenv 3.12 eyecue-things-api
pyenv activate eyecue-things-api

# Install dependencies
make install-dev
```

To run the tests:

```bash
make test
# or
pytest
# or, a specific test
pytest -k name_of_my_test
```

> ⚠️ **Note**: The tests use localstack so make sure you have Docker installed and running.

## Deployment

### Option 1: Manually via Bitbucket Pipelines

You can deploy this project via the Bitbucket pipelines.

Go to: `Pipelines` ► `Run pipeline` ► Select your `Branch` and the `Pipeline` called `deploy-qa` ► `Run` and the pipeline will deploy the API to the QA account.

If you want to deploy to all customer accounts, you can run the `deploy-customer` pipeline instead. Note that this will only work from the `master` branch.

### Option 2: Via pushing a tag

- **Development Deployment**: Push a tag in the format `1.0.0-anything` to deploy the API to the QA account.
- **Production Deployment**: Push a tag in the format `1.0.0` to deploy the API to all customer accounts. Note that this will only work from the `master` branch.

### Option 3: Via Serverless locally

You can deploy to the QA account by running the following commands:

> ⚠️ **Note**: This will only work if you are updating the stack, it will not work if you are
> creating it for the first time as the PowerAccess role does not have the necessary permissions.

```bash
# Copy the .env.example file. This file contains the environment variables needed to deploy the API.
cp .env.example .env

# You will need to assume PowerAccess role in the QA account to deploy the API.
# You can do this by setting the `AWS_PROFILE` environment variable in the .env file.
# Or you can assume the role manually by running the following command:
source <(fm aws assume-role qa-aus --role PowerAccess)

# Install the serverless framework and the dependencies
npm install

# Deploy the API
npm run deploy
```
