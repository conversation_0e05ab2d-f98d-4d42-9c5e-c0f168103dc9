from collections.abc import Mapping
from typing import Any

from mypy_boto3_dynamodb import DynamoDBServiceResource
from mypy_boto3_dynamodb.service_resource import Table

from eyecue_things_api.models import Server, Tracker
from eyecue_things_api.models.tracker import Roi
from eyecue_things_api.models.tracker.indoor_roi import IndoorRoi
from eyecue_things_api.models.tracker.indoor_tracker import IndoorTracker
from eyecue_things_api.models.types import ServerId, SiteId, TrackerId
from eyecue_things_api.util import convert_floats_to_decimal


def insert_dynamodb_item(table: Table, item: dict[str, Any]) -> None:
    item = convert_floats_to_decimal(item)
    table.put_item(Item=item)


def create_dynamodb_table(table_name: str, dynamodb: DynamoDBServiceResource) -> None:
    dynamodb.create_table(
        TableName=table_name,
        KeySchema=[
            {"AttributeName": "site_id", "KeyType": "HASH"},
            {"AttributeName": "camera_id", "KeyType": "RANGE"},
        ],
        AttributeDefinitions=[
            {"AttributeName": "site_id", "AttributeType": "S"},
            {"AttributeName": "camera_id", "AttributeType": "S"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
    )


def delete_dynamodb_table(table: Table) -> None:
    table.delete()
    table.wait_until_not_exists()


class TrackerFactory:
    def create_roi(
        self,
        roi_id: str = "deliver",
        **kwargs: Any,
    ) -> Roi:
        """Create an ROI object with some sensible defaults."""
        roi_defaults = {
            "id": roi_id,
            "type": "DWELL",
            "coordinates": [[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]],
            "functionalities": ["timer"],
        }

        return Roi.model_validate(deep_merge(roi_defaults, kwargs))

    def create_indoor_roi(
        self,
        roi_id: str = "deliver",
        **kwargs: Any,
    ) -> IndoorRoi:
        """Create an Indoor ROI object with some sensible defaults."""
        roi_defaults = {
            "id": roi_id,
            "type": "chip_bay|chip_bay_state",
            "coordinates": [[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]],
        }

        return IndoorRoi.model_validate(deep_merge(roi_defaults, kwargs))

    @staticmethod
    def create_tracker(
        site_id: SiteId = "fm-tst-aus-0318",
        camera_id: TrackerId = "eyeq-tracker-fm-tst-aus-0318-camera001",
        author_name: str = "<EMAIL>",
        **kwargs: Any,
    ) -> Tracker:
        """Create a Tracker object with some sensible defaults."""
        tracker_defaults = {
            "site_id": site_id,
            "camera_id": camera_id,
            "author_name": author_name,
            "camera": {
                "camera_id": camera_id.split("-camera")[1],
                "source": "rtsp://localhost:8554/stream",
            },
            "detector": {
                "classes": [
                    {"name": "car"},
                    {"name": "person"},
                ],
            },
            "rois": [],
        }

        return Tracker.model_validate(deep_merge(tracker_defaults, kwargs))

    @staticmethod
    def create_tracker_indoor(
        site_id: SiteId = "fm-tst-aus-0318",
        camera_id: TrackerId = "eyeq-tracker-fm-tst-aus-0318-camera001",
        author_name: str = "<EMAIL>",
        **kwargs: Any,
    ) -> IndoorTracker:
        """Create a Tracker object with some sensible defaults."""
        tracker_defaults = {
            "site_id": site_id,
            "camera_id": camera_id,
            "author_name": author_name,
            "camera": {
                "camera_id": camera_id.split("-camera")[1],
                "source": "rtsp://localhost:8554/stream",
            },
            "detector": {
                "classes": [
                    {"name": "person"},
                    {"name": "car"},
                ],
            },
            "rois": [],
        }

        return IndoorTracker.model_validate(deep_merge(tracker_defaults, kwargs))


class ServerFactory:
    @staticmethod
    def create_server(
        site_id: SiteId = "fm-tst-aus-0318",
        camera_id: ServerId = "eyeq-server-fm-tst-aus-0318",
        author_name: str = "<EMAIL>",
        **kwargs: Any,
    ) -> Server:
        """Create a Server object with some sensible defaults."""
        server_defaults = {
            "site_id": site_id,
            "camera_id": camera_id,
            "author_name": author_name,
            "configuration": {
                "topic": "some-topic",
            },
        }
        return Server.model_validate(deep_merge(server_defaults, kwargs))


def deep_merge(d1: dict[str, Any], d2: dict[str, Any]) -> dict[str, Any]:
    """Merge two dictionaries recursively."""
    merged = d1.copy()
    for key, value in d2.items():
        if (
            isinstance(value, Mapping)
            and key in merged
            and isinstance(merged[key], Mapping)
        ):  # pragma: no cover
            merged[key] = deep_merge(merged[key], value)  # type: ignore[arg-type]
        else:
            merged[key] = value
    return merged
