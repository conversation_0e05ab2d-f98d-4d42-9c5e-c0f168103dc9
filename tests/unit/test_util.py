# mypy: ignore-errors
import uuid as uuid_lib
from http import HTTPStatus

import pytest
from botocore.exceptions import ClientError

from eyecue_things_api.exceptions import ItemExistsError, ROINotFoundError
from eyecue_things_api.util import (
    exception_handler,
    find_roi,
    recursive_merge_with_null_deletions,
)
from tests.util import TrackerFactory


def test_handler_item_exists_error() -> None:
    exception = ItemExistsError(
        "site_id",
        "camera_id",
    )
    response = exception_handler(exception)

    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert response.body.error == "Bad Request"
    assert response.body.message == (
        "Item with site_id=site_id and camera_id=camera_id already exists"
    )


def test_handler_botocore_error() -> None:
    exception = ClientError(
        error_response={
            "Error": {
                "Code": "AccessDeniedException",
                "Message": "Access Denied",
            }
        },
        operation_name="PutItem",
    )
    response = exception_handler(exception)

    assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
    assert response.body.error == "Internal Server Error"
    assert response.body.message == "An error occurred during an AWS operation"


def test_handler_catch_all() -> None:
    exception = Exception("Some other exception")
    response = exception_handler(exception)

    assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
    assert response.body.error == "Internal Server Error"
    assert response.body.message == "An unexpected error occurred"


def test_recursive_merge_with_null_deletions() -> None:
    old_json = {
        "a": 123,
        "b": "remove",
        "c": [
            {"a": 1, "b": 2},
            [{"a": 3, "b": 4}],
            [1, 2, 3],
            [{"a": "keep"}, {"a": "delete"}],
        ],
        "d": [1, 2, 3],
        "f": [0],
    }
    new_json = {
        "a": 456,
        "c": [{"a": 3}, {"a": 5, "b": 3}, [1, 2, 3], [{"a": "keep"}]],
        "d": [1, 2, 3, 4],
        "e": "new_item",
        "f": [1, 2],
    }

    merged_json = recursive_merge_with_null_deletions(old_json, new_json)
    expected_json = {
        "a": 456,
        "b": None,
        "c": [{"a": 3, "b": None}, {"a": 5, "b": 3}, [1, 2, 3], [{"a": "keep"}]],
        "d": [1, 2, 3, 4],
        "e": "new_item",
        "f": [1, 2],
    }
    assert merged_json == expected_json


def test_find_roi() -> None:
    tracker_factory = TrackerFactory()
    tracker = tracker_factory.create_tracker()
    roi_1 = tracker_factory.create_roi()
    roi_2 = tracker_factory.create_roi(roi_id="deliver2")

    random_fake_uuid = uuid_lib.uuid4
    with pytest.raises(ROINotFoundError) as exc_info:
        _, _ = find_roi(tracker, random_fake_uuid)

    assert str(random_fake_uuid) in str(exc_info.value)
    assert "fm-tst-aus-0318" in str(exc_info.value)
    assert "eyeq-tracker-fm-tst-aus-0318-camera001" in str(exc_info.value)

    tracker.rois.append(roi_1)
    tracker.rois.append(roi_2)

    with pytest.raises(ROINotFoundError) as exc_info:
        _, _ = find_roi(tracker, random_fake_uuid)

    assert str(random_fake_uuid) in str(exc_info.value)
    assert "fm-tst-aus-0318" in str(exc_info.value)
    assert "eyeq-tracker-fm-tst-aus-0318-camera001" in str(exc_info.value)

    index, roi = find_roi(tracker, roi_1.uuid)
    assert index == 0
    assert roi == roi_1

    index, roi = find_roi(tracker, roi_2.uuid)
    assert index == 1
    assert roi == roi_2
