from http import HTTPStatus

import pytest
from pydantic_core import PydanticSerializationError

from eyecue_things_api.response import ValidationErrorBody, ValidationErrorResponse


@pytest.fixture
def error_response() -> ValidationErrorResponse:
    return ValidationErrorResponse(
        status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value,
        body=ValidationErrorBody(
            message="Validation error",
            error="ValidationError",
            details=[
                {
                    "loc": ("body", "name"),
                    "msg": "field required",
                    "type": "value_error.missing",
                    "input": None,
                }
            ],
        ),
    )


def test_validation_error_response_serialization_value_error(
    error_response: ValidationErrorResponse,
) -> None:
    error_response.body.details[0].setdefault(
        "ctx",
        {
            "error": ValueError("field required"),
        },
    )

    assert error_response.model_dump(mode="json") == {
        "statusCode": 500,
        "body": {
            "message": "Validation error",
            "error": "ValidationError",
            "details": [
                {
                    "loc": ["body", "name"],
                    "msg": "field required",
                    "type": "value_error.missing",
                    "input": None,
                    "ctx": {"error": "field required"},
                }
            ],
        },
    }


def test_validation_error_response_serialization_other_error(
    error_response: ValidationErrorResponse,
) -> None:
    error_response.body.details[0].setdefault(
        "ctx",
        {
            "error": TypeError("field required"),
        },
    )

    with pytest.raises(
        PydanticSerializationError,
        match="Unable to serialize unknown type: <class 'TypeError'>",
    ):
        error_response.model_dump(mode="json")


def test_validation_error_response_serialization_no_error(
    error_response: ValidationErrorResponse,
) -> None:
    assert error_response.model_dump(mode="json") == {
        "statusCode": 500,
        "body": {
            "message": "Validation error",
            "error": "ValidationError",
            "details": [
                {
                    "loc": ["body", "name"],
                    "msg": "field required",
                    "type": "value_error.missing",
                    "input": None,
                }
            ],
        },
    }
