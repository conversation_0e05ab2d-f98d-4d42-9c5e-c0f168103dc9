import re
from pathlib import Path

import yaml
from aws_lambda_powertools.event_handler.router import APIGatewayRouter

from eyecue_things_api.routes.router import router as main_router

API_URL_PREFIX = "/api/v1"


def normalize_path(path: str) -> str:
    # Convert {var} -> <var> to match Python syntax
    return re.sub(r"\{(\w+)\}", r"<\1>", path.strip("/"))


def extract_vars(path: str) -> set:
    return set(re.findall(r"<(\w+)>", path))


def get_yaml_routes(path: str = "serverless.yaml"):
    with Path(path).open() as f:
        config = yaml.safe_load(f)

    routes = []
    for name, fn in config.get("functions", {}).items():
        if name not in ["fallback-handler"]:  # ignored function routes
            for event in fn.get("events", []):
                if "http" in event:
                    method = event["http"]["method"].upper()
                    raw_path = "/" + normalize_path(event["http"]["path"])
                    raw_path = raw_path.replace(API_URL_PREFIX, "")
                    var_names = frozenset(extract_vars(raw_path))
                    routes.append((method, raw_path, var_names))
    return routes


def get_python_routes(router: APIGatewayRouter):
    routes = []
    for route in router._routes:  # noqa: SLF001
        path = route[0]
        method = route[1][0]
        var_names = frozenset(extract_vars(path))
        routes.append((method, path, var_names))
    return routes


def test_route_sync() -> None:
    yaml_routes = set(get_yaml_routes("serverless.yml"))
    python_routes = set(get_python_routes(main_router))

    missing_in_code = yaml_routes - python_routes
    missing_in_yaml = python_routes - yaml_routes

    assert not missing_in_code, (
        f"Declared in YAML but missing in code: {missing_in_code}"
    )
    assert not missing_in_yaml, (
        f"Implemented in code but missing in YAML: {missing_in_yaml}"
    )
