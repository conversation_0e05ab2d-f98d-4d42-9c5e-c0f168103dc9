from typing import Any, NotRequired, TypedDict

from eyecue_things_api.models.discriminator import get_discriminator_value


class ThingShadowDiscriminator(TypedDict):
    camera_id: NotRequired[Any]


def test_get_discriminator_value() -> None:
    d = ThingShadowDiscriminator(camera_id=None)

    assert get_discriminator_value(d) is None

    d["camera_id"] = "eyeq-tracker"
    assert get_discriminator_value(d) == "tracker"

    d["camera_id"] = "eyeq-server"
    assert get_discriminator_value(d) == "server"

    d["camera_id"] = "something-else"
    assert get_discriminator_value(d) is None

    del d["camera_id"]
    assert get_discriminator_value(d) is None
