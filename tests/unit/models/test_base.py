from datetime import UTC, datetime
from zoneinfo import ZoneInfo

from eyecue_things_api.models.base import ThingEntityModel


def test_thing_entity_model() -> None:
    model = ThingEntityModel(
        site_id="fm-mcd-aus-1305",
        camera_id="eyeq-server-fm-mcd-aus-1305",
        author_name="<EMAIL>",
        last_updated_timestamp=datetime(
            2021, 1, 1, 0, 0, 0, tzinfo=ZoneInfo("Pacific/Auckland")
        ),
    )

    assert model.site_id == "fm-mcd-aus-1305"
    assert model.camera_id == "eyeq-server-fm-mcd-aus-1305"
    assert model.author_name == "<EMAIL>"
    assert model.last_updated_timestamp == datetime(
        2021, 1, 1, 0, 0, 0, tzinfo=ZoneInfo("Pacific/Auckland")
    )

    # assert it serializes the last_updated_timestamp to UTC timezone in ISO format
    item = model.model_dump()
    assert item["last_updated_timestamp"] == datetime(
        2020, 12, 31, 11, 0, 0, tzinfo=UTC
    )

    item = model.model_dump(mode="json")
    assert item["last_updated_timestamp"] == "2020-12-31T11:00:00+00:00"
