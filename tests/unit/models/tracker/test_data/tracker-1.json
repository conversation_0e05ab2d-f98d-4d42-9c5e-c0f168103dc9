{"site_id": "fm-tst-aus-0318", "camera_id": "eyeq-tracker-fm-tst-aus-0318-camera150", "author_name": "<EMAIL>", "camera": {"camera_id": "150", "finish_camera": true, "frame_grabber": true, "label": "waiting_bay", "name": "fm-tst-aus-0318-150", "rotate_angle": 0, "s3_path": "eyecue-cv-qa-au-camera-images/fm-tst-aus-0318/150.jpg", "source": "rtsp://fingermark:Fingermark1!@**************/Streaming/Channels/103/"}, "config": {"log_output_path": "/root/logs/camera150", "server-url": "http://127.0.0.1:5000"}, "detector": {"classes": [{"name": "car", "threshold": 0.3}, {"name": "person", "threshold": 0.3}], "host": "localhost", "routing_key": "eyeq-detector", "stream_out": true}, "rois": [{"coordinates": [[0.****************, 0.****************], [0.****************, 0.****************], [0.*****************, 0.****************], [0.****************, 0.****************], [0.****************, 0.*****************], [0.**************, 0.*****************]], "functionalities": ["timer", "stream_roi", "best_shot", "queue"], "functional_zone": "waiting-bay", "id": "waiting_bay_1", "interaction_point": {"x": 0.5, "y": 0.5}, "interaction_roi": true, "order": "F", "overhead": true, "predict": false, "queue_level": 1, "rthreshold": 0.4, "rtype": "tbfifo", "store": false, "type": "WAITING"}]}