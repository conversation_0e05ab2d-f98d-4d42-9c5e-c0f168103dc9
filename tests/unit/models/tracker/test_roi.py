import pytest
from pydantic import ValidationError

from tests.util import TrackerFactory


def test_validates_best_shot_order(tracker_factory: TrackerFactory) -> None:
    order = "A.B.C"
    functionalities = ["best_shot"]

    with pytest.raises(
        ValidationError,
        match="order cannot be None when functionality includes best shot",
    ):
        tracker_factory.create_roi(functionalities=functionalities)

    roi = tracker_factory.create_roi(
        functionalities=functionalities,
        order=order,
    )
    assert roi.order == "A.B.C"
    assert roi.functionalities == ["best_shot"]


def test_invalid_roi_type(tracker_factory: TrackerFactory) -> None:
    with pytest.raises(
        ValidationError,
        match=("type must be one of \\[.*\\], not INVALID"),
    ):
        tracker_factory.create_roi(type="INVALID")

    with pytest.raises(ValidationError, match="Input should be a valid string"):
        tracker_factory.create_roi(type=123)
