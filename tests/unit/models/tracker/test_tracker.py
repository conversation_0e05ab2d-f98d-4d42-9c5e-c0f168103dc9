import json
from collections.abc import Iterable
from pathlib import Path
from typing import Any

import pytest
from pydantic import ValidationError

from eyecue_things_api.models import Tracker
from tests.util import TrackerFactory


def _test_data_generator() -> Iterable[dict[str, Any]]:
    cwd = Path(__file__).parent

    for f in (cwd / "test_data").glob("*.json"):
        with f.open() as file:
            yield json.load(file)


@pytest.mark.parametrize("tracker", _test_data_generator())
def test_validation(tracker: dict[str, Any]) -> None:
    """Test that the tracker model validates the test data."""
    Tracker.model_validate(tracker)


def test_validates_no_duplicate_roi_ids(tracker_factory: TrackerFactory) -> None:
    roi1 = tracker_factory.create_roi(roi_id="1")
    roi2 = tracker_factory.create_roi(roi_id="1")
    roi3 = tracker_factory.create_roi(roi_id="2")
    roi4 = tracker_factory.create_roi(roi_id="2")

    with pytest.raises(ValidationError, match="Duplicate ROI IDs found: 1, 2"):
        tracker_factory.create_tracker(rois=[roi1, roi2, roi3, roi4])


def test_validates_no_duplicate_roi_ids_indoor(tracker_factory: TrackerFactory) -> None:
    roi1 = tracker_factory.create_indoor_roi(roi_id="1")
    roi2 = tracker_factory.create_indoor_roi(roi_id="1")
    roi3 = tracker_factory.create_indoor_roi(roi_id="2")
    roi4 = tracker_factory.create_indoor_roi(roi_id="2")

    with pytest.raises(ValidationError, match="Duplicate ROI IDs found: 1, 2"):
        tracker_factory.create_tracker_indoor(rois=[roi1, roi2, roi3, roi4])
