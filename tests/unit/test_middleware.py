import pytest
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import BaseModel

from eyecue_things_api.middleware import validated_event_parser
from eyecue_things_api.model_config import model_config
from eyecue_things_api.response import LambdaResponse


def test_event_parser(lambda_context: LambdaContext) -> None:
    class DummyEvent(BaseModel):
        model_config = model_config

        some_field: str

    class DummyResponse(LambdaResponse):
        body: DummyEvent

    @validated_event_parser(model=DummyEvent)  # type: ignore[misc]
    def lambda_handler(event: DummyEvent, _context: LambdaContext) -> LambdaResponse:
        return DummyResponse(status_code=200, body=event)

    event = DummyEvent(some_field="value")
    # MonkeyPatch is used to set the environment variable LAMBDA_TASK_ROOT
    # so that the response gets dumped to a dict rather than being returned
    # as a LambdaResponse object.
    with pytest.MonkeyPatch().context() as mp:
        mp.setenv("LAMBDA_TASK_ROOT", "/var/task")
        response = lambda_handler(event, lambda_context)

    assert isinstance(response, dict)
    assert response == {"statusCode": 200, "body": {"some_field": "value"}}
