from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.indoor.indoor_delete import IndoorTrackerDeleteEvent, lambda_handler
from tests.util import TrackerFactory, insert_dynamodb_item


def test_indoor_tracker_delete_no_item(
    lambda_context: LambdaContext,
    execution_context: Context,
) -> None:
    """Test lambda_handler for deleting an indoor tracker that does not exist."""
    event = IndoorTrackerDeleteEvent(
        site_id="fm-tst-aus-0318",
        tracker_id="eyeq-tracker-fm-tst-aus-0318-camera001",
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.NOT_FOUND
    assert response.body.error == "Not Found"
    assert response.body.message == (
        "Unable to delete item with site_id=fm-tst-aus-0318 and "
        "camera_id=eyeq-tracker-fm-tst-aus-0318-camera001. "
        "Has the item been deleted already?"
    )


def test_indoor_tracker_delete_success(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test lambda_handler for deleting an indoor_tracker successfully."""
    tracker = tracker_factory.create_tracker_indoor()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )

    event = IndoorTrackerDeleteEvent(
        site_id=tracker.site_id,
        tracker_id=tracker.camera_id,
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.NO_CONTENT
    assert response.body is None
