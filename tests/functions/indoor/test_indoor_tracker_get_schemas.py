from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.indoor.indoor_detector_get_schema import (
    lambda_handler as indoor_detector_lambda_handler,
)
from functions.indoor.indoor_roi_get_schema import (
    lambda_handler as indoor_roi_lambda_handler,
)
from functions.indoor.indoor_stream_get_schema import (
    lambda_handler as indoor_stream_lambda_handler,
)
from functions.indoor.indoor_tracker_get_schema import (
    lambda_handler as indoor_tracker_lambda_handler,
)


def test_indoor_tracker_schemas_success(
    lambda_context: LambdaContext,
    execution_context: Context,
) -> None:
    """Test lambda_handler for geting json schemas for indoor_tracker."""
    response = indoor_roi_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)

    response = indoor_detector_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)

    response = indoor_stream_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)

    response = indoor_tracker_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)
