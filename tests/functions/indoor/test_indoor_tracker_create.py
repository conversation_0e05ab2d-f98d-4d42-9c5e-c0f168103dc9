from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.indoor.indoor_create import IndoorTrackerCreateEvent
from functions.indoor.indoor_create import lambda_handler as lambda_handler_indoor
from tests.util import TrackerFactory, insert_dynamodb_item


def test_indoor_tracker_create_success(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test lambda_handler for creating an indoor tracker."""
    tracker = tracker_factory.create_tracker_indoor()
    event = IndoorTrackerCreateEvent.model_validate(tracker.model_dump())

    response = lambda_handler_indoor(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.CREATED
    assert response.body == event


def test_indoor_tracker_create_already_exists(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test lambda_handler for creating an indoor_tracker that already exists."""
    tracker = tracker_factory.create_tracker_indoor()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )

    event = IndoorTrackerCreateEvent.model_validate(tracker.model_dump())

    response = lambda_handler_indoor(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert response.body.error == "Bad Request"
    assert response.body.message == (
        "Item with site_id=fm-tst-aus-0318 and "
        "camera_id=eyeq-tracker-fm-tst-aus-0318-camera001 already exists"
    )
