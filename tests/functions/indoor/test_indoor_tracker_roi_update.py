# mypy: ignore-errors
from http import HTTPStatus

import pytest
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.exceptions import ROINotFoundError
from functions.indoor.indoor_roi_update import lambda_handler
from tests.util import TrackerFactory, insert_dynamodb_item


def test_indoor_tracker_roi_update_success(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test for updating a indoor_roi for a indoor_tracker."""
    tracker = tracker_factory.create_tracker_indoor()
    roi = tracker_factory.create_indoor_roi()
    tracker.rois.append(roi)
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )
    roi.id = "new_id"
    event = {
        "site_id": tracker.site_id,
        "tracker_id": tracker.camera_id,
        "roi": roi.model_dump(mode="json"),
    }

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert response["body"]["rois"][0] == roi.model_dump(mode="json")


def test_indoor_tracker_roi_delete_fail(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test for errors while updating a indoor_roi for an indoor_tracker."""
    tracker = tracker_factory.create_tracker_indoor()
    roi = tracker_factory.create_indoor_roi()
    tracker.rois.append(roi)
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )
    # trying to update an IndoorROI that do not exist
    roi2 = tracker_factory.create_indoor_roi()
    event = {
        "site_id": tracker.site_id,
        "tracker_id": tracker.camera_id,
        "roi": roi2.model_dump(mode="json"),
    }
    with pytest.raises(ROINotFoundError) as exc_info:
        _ = lambda_handler(
            event=event,
            context=lambda_context,
            execution_context=execution_context,
        )

    assert tracker.site_id in exc_info.value.message
    assert tracker.camera_id in exc_info.value.message
    assert str(roi2.uuid) in exc_info.value.message
