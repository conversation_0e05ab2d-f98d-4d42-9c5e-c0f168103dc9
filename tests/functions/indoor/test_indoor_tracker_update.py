from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.indoor.indoor_update import lambda_handler
from tests.util import TrackerFactory, insert_dynamodb_item


def test_indoor_tracker_update_lambda_handler(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test lambda_handler for updating an indoor_tracker successfully."""
    tracker = tracker_factory.create_tracker_indoor()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )

    tracker.author_name = "ABCDEFG"

    response = lambda_handler(
        event=tracker.model_dump(mode="json"),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert response["body"]["author_name"] == "ABCDEFG"
