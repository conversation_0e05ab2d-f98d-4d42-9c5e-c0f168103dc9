from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.indoor.camera.indoor_camera_get import (
    IndoorCameraGetEvent,
    lambda_handler,
)
from tests.util import TrackerFactory, insert_dynamodb_item


def test_get_camera_1(
    lambda_context: LambdaContext, execution_context: Context
) -> None:
    """Test getting an indoor camera does not exist."""
    response = lambda_handler(
        event=IndoorCameraGetEvent(
            site_id="fm-tst-aus-0318",
            tracker_id="eyeq-tracker-fm-tst-aus-0318-camera001",
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.NOT_FOUND
    assert response.body.error == "Not Found"
    assert response.body.message == (
        "Item with site_id=fm-tst-aus-0318 and "
        "camera_id=eyeq-tracker-fm-tst-aus-0318-camera001 does not exist"
    )


def test_get_camera_lambda_handler(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test the lambda handler for getting an indoor camera."""
    tracker = tracker_factory.create_tracker_indoor()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )

    response = lambda_handler(
        event=IndoorCameraGetEvent(
            site_id=tracker.site_id,
            tracker_id=tracker.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body == tracker.camera
