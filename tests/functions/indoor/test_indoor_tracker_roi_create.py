# mypy: ignore-errors
from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.indoor.indoor_roi_create import IndoorROICreateEvent, lambda_handler
from tests.util import TrackerFactory, insert_dynamodb_item


def test_indoor_tracker_roi_create_success(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test lambda_handler for creating an indoor roi for an indoor_tracker."""
    tracker = tracker_factory.create_tracker_indoor()
    roi = tracker_factory.create_indoor_roi()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )
    event = IndoorROICreateEvent(
        site_id=tracker.site_id, tracker_id=tracker.camera_id, roi=roi
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body.rois[0] == roi


def test_tracker_roi_create_errors(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Errors in creating an indoor roi for an indoor_tracker."""
    tracker = tracker_factory.create_tracker_indoor()
    roi = tracker_factory.create_indoor_roi()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )
    event = IndoorROICreateEvent(
        site_id=tracker.site_id, tracker_id=tracker.camera_id, roi=roi
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    # trying to add ROI that already exists
    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )
    assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR

    # trying to add ROI in a non existing store/camera
    event2 = IndoorROICreateEvent(
        site_id="fm-tst-aus-0031",
        tracker_id="eyeq-tracker-fm-tst-aus-0031-camera001",
        roi=roi,
    )
    response = lambda_handler(
        event=event2,
        context=lambda_context,
        execution_context=execution_context,
    )
    assert response.status_code == HTTPStatus.NOT_FOUND
