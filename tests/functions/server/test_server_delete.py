from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.server.server_delete import ServerDeleteEvent, lambda_handler
from tests.util import ServerFactory, insert_dynamodb_item


def test_server_delete_no_item(
    lambda_context: LambdaContext,
    execution_context: Context,
) -> None:
    """Test lambda_handler for deleting a server that does not exist."""
    event = ServerDeleteEvent(
        site_id="fm-tst-aus-0318",
        server_id="eyeq-server-fm-tst-aus-0318",
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.NOT_FOUND
    assert response.body.error == "Not Found"
    assert response.body.message == (
        "Unable to delete item with site_id=fm-tst-aus-0318 and "
        "camera_id=eyeq-server-fm-tst-aus-0318. "
        "Has the item been deleted already?"
    )


def test_server_delete_success(
    lambda_context: LambdaContext,
    execution_context: Context,
    server_factory: ServerFactory,
) -> None:
    """Test lambda_handler for deleting a server successfully."""
    server = server_factory.create_server()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=server.model_dump(mode="json"),
    )

    event = ServerDeleteEvent(
        site_id=server.site_id,
        server_id=server.camera_id,
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.NO_CONTENT
    assert response.body is None
