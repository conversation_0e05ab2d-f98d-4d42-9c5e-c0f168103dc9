from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import Lambda<PERSON>ontex<PERSON>

from eyecue_things_api.context import Context
from functions.server.server_create import ServerCreateEvent, lambda_handler
from tests.util import ServerFactory


def test_server_create_lambda_handler(
    lambda_context: LambdaContext,
    execution_context: Context,
    server_factory: ServerFactory,
) -> None:
    """Test lambda_handler for creating a server successfully."""
    server = server_factory.create_server()
    event = ServerCreateEvent.model_validate(server.model_dump(mode="json"))

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.CREATED
    assert response.body == event
