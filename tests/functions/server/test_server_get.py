from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.server.server_get import ServerGetEvent, lambda_handler
from tests.util import ServerFactory, TrackerFactory, insert_dynamodb_item


def test_server_get_1(
    lambda_context: LambdaContext, execution_context: Context
) -> None:
    """Test getting a server that does not exist."""
    response = lambda_handler(
        event=ServerGetEvent(
            site_id="fm-tst-aus-0318",
            server_id="eyeq-server-fm-tst-aus-0318",
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.NOT_FOUND
    assert response.body.error == "Not Found"
    assert response.body.message == (
        "Item with site_id=fm-tst-aus-0318 and "
        "camera_id=eyeq-server-fm-tst-aus-0318 does not exist"
    )


def test_server_get_2(
    lambda_context: LambdaContext,
    execution_context: Context,
    server_factory: ServerFactory,
) -> None:
    """Test getting a server successfully."""
    server = server_factory.create_server()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=server.model_dump(mode="json"),
    )

    response = lambda_handler(
        event=ServerGetEvent(
            site_id=server.site_id,
            server_id=server.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body == server


def test_server_get_3(
    lambda_context: LambdaContext,
    execution_context: Context,
    server_factory: ServerFactory,
) -> None:
    """Test getting a server with a validation error."""
    server = server_factory.create_server()

    # Disable validation for this test
    server.model_config["validate_assignment"] = False
    server.author_name = 123  # type: ignore[assignment]

    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=server.model_dump(mode="json", warnings=False),
    )

    response = lambda_handler(
        event=ServerGetEvent(
            site_id=server.site_id,
            server_id=server.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
    assert response.body.error == "Internal Server Error"
    assert response.body.message == (
        "Validation failed with 2 error(s):\n"
        "Field 'server.author_name.function-after[_validate(), str]': "
        "Input should be a valid string\n"
        "Field 'server.author_name.str': Input should be a valid string"
    )


def test_server_get_4(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test getting a server with a model type error."""
    tracker = tracker_factory.create_tracker()

    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json", warnings=False),
    )

    class ServerGetEventWithBadServerId(ServerGetEvent):
        # Disable validation for this test
        server_id: str

    response = lambda_handler(
        event=ServerGetEventWithBadServerId(
            site_id=tracker.site_id,
            server_id=tracker.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert response.body.error == "Bad Request"
    assert response.body.message == (
        "Expected item of type Server, but got item of type Tracker"
    )


def test_server_get_lambda_handler(
    lambda_context: LambdaContext,
    execution_context: Context,
    server_factory: ServerFactory,
) -> None:
    """Test the lambda handler for getting a server."""
    server = server_factory.create_server()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=server.model_dump(mode="json"),
    )

    response = lambda_handler(
        event=ServerGetEvent(
            site_id=server.site_id,
            server_id=server.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body == server
