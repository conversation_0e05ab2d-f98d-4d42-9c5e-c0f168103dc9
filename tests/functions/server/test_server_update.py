from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import Lambda<PERSON><PERSON>x<PERSON>

from eyecue_things_api.context import Context
from functions.server.server_update import lambda_handler
from tests.util import ServerFactory, insert_dynamodb_item


def test_server_update_lambda_handler(
    lambda_context: LambdaContext,
    execution_context: Context,
    server_factory: ServerFactory,
) -> None:
    """Test lambda_handler for updating a tracker successfully."""
    server = server_factory.create_server()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=server.model_dump(mode="json"),
    )

    server.author_name = "ABCDEFG"

    response = lambda_handler(
        event=server.model_dump(mode="json"),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert response["body"]["author_name"] == "ABCDEFG"
