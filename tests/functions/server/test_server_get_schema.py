from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.server.server_get_schema import lambda_handler


def test_server_schemas_success(
    lambda_context: LambdaContext,
    execution_context: Context,
) -> None:
    """Test lambda_handler for geting json schemas for server config."""
    response = lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)
