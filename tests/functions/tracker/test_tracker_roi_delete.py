# mypy: ignore-errors
import uuid as uuid_lib
from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.tracker.roi_delete import ROIDeleteEvent, lambda_handler
from tests.util import TrackerFactory, insert_dynamodb_item


def test_tracker_roi_delete_success(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test lambda_handler for creating a roi for a tracker."""
    tracker = tracker_factory.create_tracker()
    roi = tracker_factory.create_roi()
    tracker.rois.append(roi)
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )
    event = ROIDeleteEvent(
        site_id=tracker.site_id, tracker_id=tracker.camera_id, roi_uuid=roi.uuid
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    del tracker.rois[0]
    response.body.last_updated_timestamp = None
    tracker.last_updated_timestamp = None

    assert response.body == tracker


def test_tracker_roi_delete_fail(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test lambda_handler for errors while creating a roi for a tracker."""
    tracker = tracker_factory.create_tracker()
    roi = tracker_factory.create_roi()
    tracker.rois.append(roi)
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )
    event = ROIDeleteEvent(
        site_id=tracker.site_id,
        tracker_id=tracker.camera_id,
        roi_uuid=str(uuid_lib.uuid4()),
    )

    response = lambda_handler(
        event=event,
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
