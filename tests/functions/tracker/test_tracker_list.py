from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.tracker.tracker_list import Tracker<PERSON>ist<PERSON><PERSON>, lambda_handler
from tests.util import ServerFactory, TrackerFactory, insert_dynamodb_item


def test_tracker_list_1(
    lambda_context: LambdaContext, execution_context: Context
) -> None:
    """Test listing no trackers."""
    response = lambda_handler(
        event=TrackerListEvent(
            site_id="fm-tst-aus-0318",
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body == []


def test_tracker_list_2(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
    server_factory: ServerFactory,
) -> None:
    """Test listing multiple trackers."""
    site_id = "fm-tst-aus-0318"
    tracker1 = tracker_factory.create_tracker(
        site_id=site_id,
        camera_id="eyeq-tracker-fm-tst-aus-0318-camera001",
    )
    tracker2 = tracker_factory.create_tracker(
        site_id=site_id, camera_id="eyeq-tracker-fm-tst-aus-0318-camera002"
    )
    tracker3 = tracker_factory.create_tracker(
        site_id="fm-tst-aus-0319",  # Different site_id
        camera_id="eyeq-tracker-fm-tst-aus-0318-camera002",
    )
    server1 = server_factory.create_server(
        site_id=site_id,
        camera_id="eyeq-server-fm-tst-aus-0318",
    )

    for item in [tracker1, tracker2, tracker3, server1]:
        insert_dynamodb_item(
            table=execution_context.things_shadow_table,
            item=item.model_dump(mode="json"),
        )

    response = lambda_handler(
        event=TrackerListEvent(
            site_id=site_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body == [tracker1, tracker2]
