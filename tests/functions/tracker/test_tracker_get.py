from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.tracker.tracker_get import TrackerGetEvent, lambda_handler
from tests.util import ServerFactory, TrackerFactory, insert_dynamodb_item


def test_tracker_get_1(
    lambda_context: LambdaContext, execution_context: Context
) -> None:
    """Test getting a tracker that does not exist."""
    response = lambda_handler(
        event=TrackerGetEvent(
            site_id="fm-tst-aus-0318",
            tracker_id="eyeq-tracker-fm-tst-aus-0318-camera001",
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.NOT_FOUND
    assert response.body.error == "Not Found"
    assert response.body.message == (
        "Item with site_id=fm-tst-aus-0318 and "
        "camera_id=eyeq-tracker-fm-tst-aus-0318-camera001 does not exist"
    )


def test_tracker_get_2(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test getting a tracker successfully."""
    tracker = tracker_factory.create_tracker()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )

    response = lambda_handler(
        event=TrackerGetEvent(
            site_id=tracker.site_id,
            tracker_id=tracker.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body == tracker


def test_tracker_get_3(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test getting a tracker with a validation error."""
    tracker = tracker_factory.create_tracker()

    # Disable validation for this test
    tracker.model_config["validate_assignment"] = False
    tracker.author_name = 123  # type: ignore[assignment]

    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json", warnings=False),
    )

    response = lambda_handler(
        event=TrackerGetEvent(
            site_id=tracker.site_id,
            tracker_id=tracker.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
    assert response.body.error == "Internal Server Error"
    assert response.body.message == (
        "Validation failed with 2 error(s):\n"
        "Field 'tracker.author_name.function-after[_validate(), str]': "
        "Input should be a valid string\n"
        "Field 'tracker.author_name.str': Input should be a valid string"
    )


def test_tracker_get_4(
    lambda_context: LambdaContext,
    execution_context: Context,
    server_factory: ServerFactory,
) -> None:
    """Test getting a tracker with a model type error."""
    server = server_factory.create_server()

    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=server.model_dump(mode="json", warnings=False),
    )

    class TrackerGetEventWithBadTrackerId(TrackerGetEvent):
        # Disable validation for this test
        tracker_id: str

    response = lambda_handler(
        event=TrackerGetEventWithBadTrackerId(
            site_id=server.site_id,
            tracker_id=server.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert response.body.error == "Bad Request"
    assert response.body.message == (
        "Expected item of type Tracker, but got item of type Server"
    )


def test_tracker_get_lambda_handler(
    lambda_context: LambdaContext,
    execution_context: Context,
    tracker_factory: TrackerFactory,
) -> None:
    """Test the lambda handler for getting a tracker."""
    tracker = tracker_factory.create_tracker()
    insert_dynamodb_item(
        table=execution_context.things_shadow_table,
        item=tracker.model_dump(mode="json"),
    )

    response = lambda_handler(
        event=TrackerGetEvent(
            site_id=tracker.site_id,
            tracker_id=tracker.camera_id,
        ),
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response.status_code == HTTPStatus.OK
    assert response.body == tracker
