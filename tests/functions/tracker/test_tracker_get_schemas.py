from http import HTTPStatus

from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from functions.tracker.detector_get_schema import (
    lambda_handler as detector_lambda_handler,
)
from functions.tracker.roi_get_schema import lambda_handler as roi_lambda_handler
from functions.tracker.stream_get_schema import lambda_handler as stream_lambda_handler
from functions.tracker.tracker_get_schema import (
    lambda_handler as tracker_lambda_handler,
)


def test_tracker_schemas_success(
    lambda_context: LambdaContext,
    execution_context: Context,
) -> None:
    """Test lambda_handler for geting json schemas for tracker."""
    response = roi_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)

    response = detector_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)

    response = stream_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)

    response = tracker_lambda_handler(
        event={},
        context=lambda_context,
        execution_context=execution_context,
    )

    assert response["status_code"] == HTTPStatus.OK
    assert isinstance(response["body"], dict)
