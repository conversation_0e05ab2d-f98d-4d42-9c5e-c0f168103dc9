image: python:3.12

definitions:
  variables:
    pipeVersion: &pipeVersion fingermarkltd/eyecue-serverless-pipeline:2.0.0

    defaultPipeVariables: &defaultPipeVariables
      NODE_VERSION: v22.14.0
      PYTHON_VERSION: "3.12"
      RUNTIME_LANGUAGE: python

  steps:
    - step: &check-master
        image: atlassian/default-image:4
        name: Check master branch
        script:
          - |
            BRANCH_CURRENT=$(git rev-parse --abbrev-ref HEAD)

            if [[ "${BRANCH_CURRENT}" != "master" ]]; then
              echo "ERROR: Only master branch allowed"
              exit 1
            fi

pipelines:
  # Lint and test on every push
  default:
    - step:
        name: Lint and test
        script:
          - make install-dev
          - make lint
          - mypy --junit-xml="test-results/mypy-results.xml"
          - pytest --junitxml="test-results/test-results.xml"
        caches:
          - pip
        services:
          - docker # Required for localstack
        condition:
          changesets:
            includePaths:
              - "**.py"

  # Deploy to qa when merged with master
  branches:
    master:
      - step: &deploy-qa
          name: Serverless Deploy (qa)
          oidc: true
          script:
            - pipe: *pipeVersion
              variables:
                <<: *defaultPipeVariables
                ACCOUNT_TAG: qa-aus

  tags:
    # Development tags deploy to QA
    "*[0-9].*[0-9].*[0-9]-*":
      - step: *deploy-qa

    # Production tags deploy to prod
    "*[0-9].*[0-9].*[0-9]":
      - step: *check-master
      - step: &deploy-customer
          name: Serverless Deploy (prod)
          oidc: true
          script:
            - pipe: *pipeVersion
              variables:
                <<: *defaultPipeVariables
                ACCOUNT_TAG: customer
                ENVIRONMENT: prod

  custom:
    # deploy to qa whenever you want
    deploy-qa:
      - step: *deploy-qa

    deploy-dev:
      - step:
          name: Serverless Deploy (dev-nzl)
          oidc: true
          script:
            - pipe: *pipeVersion
              variables:
                <<: *defaultPipeVariables
                ACCOUNT_TAG: dev-nzl
      - step:
          name: Serverless Deploy (stg-nzl)
          oidc: true
          script:
            - pipe: *pipeVersion
              variables:
                <<: *defaultPipeVariables
                ACCOUNT_TAG: stg-nzl

    # deploy to prod only from master
    deploy-customer:
      - step: *check-master
      - step: *deploy-customer

    generate-openapi-schema:
      - step:
          name: Auto update OpenAPI schema and Python client
          image: python:3.12
          script:
            - export AWS_DEFAULT_REGION=us-east-1
            - make install-dev
            # Update OpenAPI schema
            - python scripts/dump_openapi_schema.py
            - git add openapi.yaml
            - |
              if git diff --cached --exit-code --quiet; then
                  echo "OpenAPI schema is up to date. No changes to commit."
              else
                  git commit -m "Update OpenAPI schema [skip ci]"
                  git push --atomic
              fi

    bump-version:
      - variables:
          - name: INCREMENT
            default: PATCH
            allowed-values:
              - MAJOR # x.0.0
              - MINOR # 0.x.0
              - PATCH # 0.0.x
      - step: *check-master
      - step:
          name: Bump version
          image: commitizen/commitizen:3.4.0
          script:
            - cz bump --increment $INCREMENT --yes
            - VERSION=$(cz version --project)
            - git push --atomic origin master $VERSION
